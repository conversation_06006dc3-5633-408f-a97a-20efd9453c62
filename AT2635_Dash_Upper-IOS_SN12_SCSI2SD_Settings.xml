<SCSI2SD>
<S2S_BoardCfg>
	<!-- ********************************************************
	Enable the onboard active terminator.
	Both ends of the SCSI chain should be terminated. Disable
	only if the SCSI2SD is in the middle of a chain with other
	devices.
	********************************************************* -->
	<enableTerminator>false</enableTerminator>
	<unitAttention>false</unitAttention>
	<parity>false</parity>
	<!-- ********************************************************
	Only set to true when using with a fast SCSI2 host
 	controller. This can cause problems with older/slower
	hardware.
	********************************************************* -->
	<enableScsi2>false</enableScsi2>
	<!-- ********************************************************
	Respond to very short duration selection attempts. This supports
	non-standard hardware, but is generally safe to enable.
	Required for Philips P2000C.
	********************************************************* -->
	<selLatch>false</selLatch>
	<!-- ********************************************************
	Convert luns to IDs. The unit must already be configured to respond
	on the ID. Allows dual drives to be accessed from a 
	XEBEC S1410 SASI bridge.
	eg. Configured for dual drives as IDs 0 and 1, but the XEBEC will
	access the second disk as ID0, lun 1.
	See ttp://bitsavers.trailing-edge.com/pdf/xebec/104524C_S1410Man_Aug83.pdf
	********************************************************* -->
	<mapLunsToIds>false</mapLunsToIds>
	<!-- ********************************************************
	Delay (in milliseconds) before responding to a SCSI selection.
	255 (auto) sets it to 0 for SCSI2 hosts and 1ms otherwise.
	Some samplers need this set to 1 manually.
	********************************************************* -->
	<selectionDelay>255</selectionDelay>
	<!-- ********************************************************
	Startup delay (in seconds) before responding to the SCSI bus 
	after power on. Default = 0.
	********************************************************* -->
	<startupDelay>0</startupDelay>
	<!-- ********************************************************
	Speed limit the SCSI interface. This is the -max- speed the 
	device will run at. The actual spee depends on the capability
	of the host controller.
	0	No limit
	1	Async 1.5MB/s
	2	Async 3.3MB/s
	3	Async 5MB/s
	4	Sync 5MB/s
	5	Sync 10MB/s
	********************************************************* -->
	<scsiSpeed>0</scsiSpeed>
	<!-- ********************************************************
	Enable SD card blind writes, which starts writing to the SD
	card before all the SCSI data has been received. Can cause problems
	with some SCSI hosts
	********************************************************* -->
	<blindWrites>false</blindWrites>
</S2S_BoardCfg>
<SCSITarget id="0">
	<enabled>true</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>2936013</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor>DASH8   </vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>IRIS USD80      </prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision>12  </revision>

	<!-- 16 character serial number -->
	<serial>07182024        </serial>

</SCSITarget>
<SCSITarget id="1">
	<enabled>false</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>4194303</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor> codesrc</vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>         SCSI2SD</prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision> 6.0</revision>

	<!-- 16 character serial number -->
	<serial>1234567812345678</serial>

</SCSITarget>
<SCSITarget id="2">
	<enabled>false</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>4194303</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor> codesrc</vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>         SCSI2SD</prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision> 6.0</revision>

	<!-- 16 character serial number -->
	<serial>1234567812345678</serial>

</SCSITarget>
<SCSITarget id="3">
	<enabled>false</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>4194303</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor> codesrc</vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>         SCSI2SD</prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision> 6.0</revision>

	<!-- 16 character serial number -->
	<serial>1234567812345678</serial>

</SCSITarget>
<SCSITarget id="4">
	<enabled>false</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>4194303</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor> codesrc</vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>         SCSI2SD</prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision> 6.0</revision>

	<!-- 16 character serial number -->
	<serial>1234567812345678</serial>

</SCSITarget>
<SCSITarget id="5">
	<enabled>false</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>4194303</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor> codesrc</vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>         SCSI2SD</prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision> 6.0</revision>

	<!-- 16 character serial number -->
	<serial>1234567812345678</serial>

</SCSITarget>
<SCSITarget id="6">
	<enabled>false</enabled>

	<!-- ********************************************************
	Space separated list. Available options:
	apple		Returns Apple-specific mode pages
	omti		OMTI host non-standard link control
	xebec		XEBEC ignore step options in control byte
	********************************************************* -->
	<quirks></quirks>


	<!-- ********************************************************
	0x0    Fixed hard drive.
	0x1    Removable drive.
	0x2    Optical drive  (ie. CD drive).
	0x3    1.44MB Floppy Drive.
	********************************************************* -->
	<deviceType>0x0</deviceType>


	<!-- ********************************************************
	Device type modifier is usually 0x00. Only change this if your
	OS requires some special value.

	0x4C    Data General Micropolis disk
	********************************************************* -->
	<deviceTypeModifier>0x0</deviceTypeModifier>


	<!-- ********************************************************
	SD card offset, as a sector number (always 512 bytes).
	********************************************************* -->
	<sdSectorStart>0</sdSectorStart>


	<!-- ********************************************************
	Drive geometry settings.
	********************************************************* -->

	<scsiSectors>4194303</scsiSectors>
	<bytesPerSector>512</bytesPerSector>
	<sectorsPerTrack>63</sectorsPerTrack>
	<headsPerCylinder>255</headsPerCylinder>


	<!-- ********************************************************
	Drive identification information. The SCSI2SD doesn't
	care what these are set to. Use these strings to trick a OS
	thinking a specific hard drive model is attached.
	********************************************************* -->

	<!-- 8 character vendor string -->
	<!-- For Apple HD SC Setup/Drive Setup, use ' SEAGATE' -->
	<vendor> codesrc</vendor>

	<!-- 16 character produce identifier -->
	<!-- For Apple HD SC Setup/Drive Setup, use '          ST225N' -->
	<prodId>       SCSI2SD  </prodId>

	<!-- 4 character product revision number -->
	<!-- For Apple HD SC Setup/Drive Setup, use '1.0 ' -->
	<revision> 6.0</revision>

	<!-- 16 character serial number -->
	<serial>1234567812345678</serial>

</SCSITarget>
</SCSI2SD>
