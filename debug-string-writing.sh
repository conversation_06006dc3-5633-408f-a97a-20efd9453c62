#!/bin/bash

# Debug script for string writing

echo "=== Debug String Writing ==="

test_file="/tmp/string_test.bin"
dd if=/dev/zero of="$test_file" bs=128 count=1 2>/dev/null

# Function to write string with padding
write_string() {
    local file="$1"
    local offset="$2"
    local string="$3"
    local max_length="$4"
    
    echo "Writing '$string' at offset $offset, max length $max_length"
    
    # Truncate or pad string to exact length
    local padded_string=$(printf "%-${max_length}s" "$string" | cut -c1-$max_length)
    echo "Padded string: '$padded_string'"
    
    # Write each character
    for ((i=0; i<max_length; i++)); do
        local char="${padded_string:$i:1}"
        if [ -z "$char" ]; then
            char=" "
        fi
        printf "$char" | dd of="$file" bs=1 count=1 seek=$((offset + i)) conv=notrunc 2>/dev/null
    done
}

# Test writing strings
write_string "$test_file" 0 "DASH8" 8
write_string "$test_file" 8 "IRIS USD80" 16
write_string "$test_file" 24 "12" 4
write_string "$test_file" 28 "07182024" 16

echo
echo "=== Hexdump of test file ==="
hexdump -C "$test_file"

echo
echo "=== Reading back strings ==="

# Read vendor (8 bytes at offset 0)
vendor_check=$(dd if="$test_file" bs=1 count=8 skip=0 2>/dev/null | strings)
echo "Vendor: '$vendor_check'"

# Read product ID (16 bytes at offset 8)
product_check=$(dd if="$test_file" bs=1 count=16 skip=8 2>/dev/null | strings)
echo "Product ID: '$product_check'"

# Read revision (4 bytes at offset 24)
revision_check=$(dd if="$test_file" bs=1 count=4 skip=24 2>/dev/null | strings)
echo "Revision: '$revision_check'"

# Read serial (16 bytes at offset 28)
serial_check=$(dd if="$test_file" bs=1 count=16 skip=28 2>/dev/null | strings)
echo "Serial: '$serial_check'"

echo
echo "=== Alternative reading method ==="

# Try without strings filter
vendor_check2=$(dd if="$test_file" bs=1 count=8 skip=0 2>/dev/null | tr -d '\0')
echo "Vendor (no strings): '$vendor_check2'"

product_check2=$(dd if="$test_file" bs=1 count=16 skip=8 2>/dev/null | tr -d '\0')
echo "Product ID (no strings): '$product_check2'"

revision_check2=$(dd if="$test_file" bs=1 count=4 skip=24 2>/dev/null | tr -d '\0')
echo "Revision (no strings): '$revision_check2'"

serial_check2=$(dd if="$test_file" bs=1 count=16 skip=28 2>/dev/null | tr -d '\0')
echo "Serial (no strings): '$serial_check2'"

rm -f "$test_file"
