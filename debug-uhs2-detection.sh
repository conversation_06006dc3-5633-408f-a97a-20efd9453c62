#!/bin/bash

# Debug UHS-II slot detection

echo "=== Debugging UHS-II Slot Detection ==="
echo

# Source the functions
source usr/sbin/ocs-flyright-functions

echo "=== Testing detect_uhs2_slot function ==="
echo

# Test the function directly
echo "Calling detect_uhs2_slot..."
uhs2_result=$(detect_uhs2_slot)
exit_code=$?

echo "Exit code: $exit_code"
echo "Result: '$uhs2_result'"

echo
echo "=== Manual UHS-II detection ==="

for file in /dev/sd*; do
    if [ ! -b "$file" ]; then
        continue
    fi

    dev="$file"
    echo "Checking device: $dev"
    
    # Check for UHS-II specific identifiers from udev rules
    udev_info=$(udevadm info --query=all --name="$dev" 2>/dev/null)
    
    echo "  Udev info:"
    echo "$udev_info" | grep -E "(ID_MODEL|ID_INSTANCE|ID_VENDOR)" | sed 's/^/    /'
    
    # Check the specific patterns
    if echo "$udev_info" | grep -q 'ID_MODEL.*SD_UHS_II'; then
        echo "  ✓ Found ID_MODEL with SD_UHS_II"
    else
        echo "  ✗ No ID_MODEL with SD_UHS_II"
    fi
    
    if echo "$udev_info" | grep -q 'ID_INSTANCE.*0:1'; then
        echo "  ✓ Found ID_INSTANCE with 0:1"
    else
        echo "  ✗ No ID_INSTANCE with 0:1"
    fi
    
    # Check if both conditions match
    if echo "$udev_info" | grep -q 'ID_MODEL.*SD_UHS_II' && \
       echo "$udev_info" | grep -q 'ID_INSTANCE.*0:1'; then
        echo "  🚀 THIS IS THE UHS-II SLOT: $dev"
    fi
    
    echo
done

echo "=== Summary ==="
if [ $exit_code -eq 0 ] && [ -n "$uhs2_result" ]; then
    echo "✓ UHS-II slot detected: $uhs2_result"
else
    echo "✗ UHS-II slot not detected"
fi
