#!/bin/bash

# Test script for blank SD card menu functionality

echo "=== Testing Blank SD Card Menu Functionality ==="
echo

# Source the functions
source usr/sbin/ocs-flyright-functions

# Find UHS-II device
echo "=== Finding UHS-II Device ==="
device=$(detect_uhs2_slot)

if [ $? -eq 0 ] && [ -n "$device" ]; then
    echo "✓ Found UHS-II slot device: $device"
else
    echo "✗ UHS-II slot not found, using fallback device"
    device="/dev/sdd"
fi

echo "Using device: $device"

# Test XML file discovery
echo
echo "=== Testing XML File Discovery ==="
xml_dir="/home/<USER>"
echo "Looking for XML files in $xml_dir..."

xml_files=()
while IFS= read -r -d '' file; do
    xml_files+=("$file")
done < <(find "$xml_dir" -name "*.xml" -type f -print0 2>/dev/null)

echo "Found ${#xml_files[@]} XML files:"
for xml_file in "${xml_files[@]}"; do
    echo "  - $(basename "$xml_file")"
done

if [ ${#xml_files[@]} -eq 0 ]; then
    echo "✗ No XML files found - menu would show error"
    exit 1
else
    echo "✓ XML files found - menu would show selection"
fi

echo
echo "=== Testing Menu Options Creation ==="

# Create menu options like the real function would
menu_options=()
for xml_file in "${xml_files[@]}"; do
    basename=$(basename "$xml_file")
    menu_options+=("$xml_file" "$basename")
done

# Add cancel option
menu_options+=("Cancel" "Cancel and exit")

echo "Menu options that would be created:"
for ((i=0; i<${#menu_options[@]}; i+=2)); do
    echo "  Option: '${menu_options[i]}' -> '${menu_options[i+1]}'"
done

echo
echo "=== Testing Config File Validation ==="

# Test each XML file
for xml_file in "${xml_files[@]}"; do
    echo "Testing XML file: $(basename "$xml_file")"
    
    if [ -f "$xml_file" ]; then
        echo "  ✓ File exists"
        
        # Test parsing key values
        enabled=$(parse_xml_target_value "$xml_file" "0" "enabled")
        vendor=$(parse_xml_target_value "$xml_file" "0" "vendor")
        product=$(parse_xml_target_value "$xml_file" "0" "prodId")
        revision=$(parse_xml_target_value "$xml_file" "0" "revision")
        
        echo "  Target 0 enabled: '$enabled'"
        echo "  Vendor: '$vendor'"
        echo "  Product: '$product'"
        echo "  Revision: '$revision'"
        
        if [ "$enabled" = "true" ] && [ -n "$vendor" ] && [ -n "$product" ]; then
            echo "  ✓ Valid configuration file"
        else
            echo "  ⚠ Configuration may be incomplete"
        fi
    else
        echo "  ✗ File does not exist"
    fi
    echo
done

echo "=== Simulating Blank SD Card Detection ==="

# Check if the current device has SCSI2SD config
echo "Checking for SCSI2SD config on $device..."

# Read last 2 sectors
device_size=$(sudo blockdev --getsize64 "$device" 2>/dev/null)
if [ $? -eq 0 ] && [ -n "$device_size" ]; then
    device_sectors=$((device_size / 512))
    config_start_sector=$((device_sectors - 2))
    
    echo "Device size: $device_size bytes"
    echo "Reading config from sectors $config_start_sector-$((device_sectors - 1))"
    
    config_check_file="/tmp/blank_sd_test.bin"
    sudo dd if="$device" of="$config_check_file" bs=512 count=2 skip=$config_start_sector 2>/dev/null
    
    if [ -f "$config_check_file" ]; then
        magic=$(dd if="$config_check_file" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')
        
        if [ "$magic" = "42434647" ]; then
            echo "✓ Valid SCSI2SD config found - would NOT show blank SD menu"
        else
            echo "✗ No valid SCSI2SD config found - WOULD show blank SD menu"
            echo "This is the scenario where the new functionality would activate"
        fi
        
        rm -f "$config_check_file"
    else
        echo "✗ Could not read device - would show error"
    fi
else
    echo "✗ Could not get device size - would show error"
fi

echo
echo "=== Integration Flow Summary ==="
echo "When a blank SD card is detected during backup operation:"
echo "1. ✓ show_blank_sd_card_menu() would be called"
echo "2. ✓ User sees warning about blank SD cards"
echo "3. ✓ User can choose 'Write Config' or 'Cancel'"
echo "4. ✓ If 'Write Config' selected, XML file menu appears"
echo "5. ✓ User selects from ${#xml_files[@]} available XML files"
echo "6. ✓ Config gets written to SD card"
echo "7. ✓ Script restarts SD card selection"
echo "8. ✓ Now SD card has config and backup can proceed"

echo
echo "=== Test Complete ==="
echo "The integration is ready for testing with a real blank SD card!"
