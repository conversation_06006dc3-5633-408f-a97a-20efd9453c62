#!/bin/bash

# Test script to verify the SCSI2SD revision field parsing fix

echo "=== Testing SCSI2SD Revision Field Parsing Fix ==="
echo

# Source the functions
source usr/sbin/ocs-flyright-functions

# Test device (adjust as needed)
device="/dev/sdm"

echo "Testing device: $device"
echo

echo "Requesting sudo access..."
sudo -v

echo
echo "=== Testing get_scsi2sd_serial_number function ==="

# Test the function
serial_result=$(get_scsi2sd_serial_number "$device")
exit_code=$?

echo
echo "Function exit code: $exit_code"
echo "Serial number result: '$serial_result'"

if [ $exit_code -eq 0 ] && [ -n "$serial_result" ]; then
    echo "✓ SUCCESS: Serial number extracted successfully"
    
    # Verify it's the revision number (should be "10" for MASTER_IRIS)
    if [ "$serial_result" = "10" ]; then
        echo "✓ CORRECT: Got expected revision number '10'"
    else
        echo "⚠ WARNING: Got '$serial_result', expected '10' for MASTER_IRIS device"
    fi
else
    echo "✗ FAILED: Could not extract serial number"
fi

echo
echo "=== Manual verification of config structure ==="

# Get device info
device_size=$(sudo blockdev --getsize64 "$device")
device_sectors=$((device_size / 512))
config_start_sector=$((device_sectors - 2))

echo "Reading config from sectors $config_start_sector-$((device_sectors - 1))..."

# Read config
config_file="/tmp/test_config.bin"
sudo dd if="$device" of="$config_file" bs=512 count=2 skip=$config_start_sector 2>/dev/null

if [ -f "$config_file" ]; then
    echo "Config file size: $(stat -c%s "$config_file") bytes"
    
    # Check magic
    magic=$(dd if="$config_file" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')
    echo "Magic signature: $magic"
    
    if [ "$magic" = "42434647" ]; then
        echo "✓ Valid SCSI2SD config detected"
        
        # Parse first target (offset 128)
        target_offset=128
        
        # Check if target is enabled
        scsi_id_byte=$(dd if="$config_file" bs=1 count=1 skip=$target_offset 2>/dev/null | od -tu1 -An | tr -d ' ')
        echo "Target 0 SCSI ID byte: $scsi_id_byte"
        
        if [ -n "$scsi_id_byte" ] && [ "$scsi_id_byte" != "0" ]; then
            echo "✓ Target 0 is enabled"
            
            # Extract fields manually for verification
            echo
            echo "Manual field extraction:"
            
            # Vendor (offset 18, 8 bytes)
            vendor_offset=$((target_offset + 18))
            vendor=$(dd if="$config_file" bs=1 count=8 skip=$vendor_offset 2>/dev/null | tr -d '\0' | strings)
            echo "Vendor: '$vendor'"
            
            # Product ID (offset 26, 16 bytes)
            product_offset=$((target_offset + 26))
            product=$(dd if="$config_file" bs=1 count=16 skip=$product_offset 2>/dev/null | tr -d '\0' | strings)
            echo "Product ID: '$product'"
            
            # Revision (offset 42, 4 bytes) - this is what we want as serial
            revision_offset=$((target_offset + 42))
            revision=$(dd if="$config_file" bs=1 count=4 skip=$revision_offset 2>/dev/null | tr -d '\0' | strings)
            echo "Revision: '$revision'"
            
            # Serial (offset 46, 16 bytes)
            serial_offset=$((target_offset + 46))
            serial=$(dd if="$config_file" bs=1 count=16 skip=$serial_offset 2>/dev/null | tr -d '\0' | strings)
            echo "Serial: '$serial'"
            
            echo
            echo "Expected: Device='MASTER_IRIS', Revision='10'"
            echo "Actual: Product='$product', Revision='$revision'"
            
            if [ "$product" = "MASTER_IRIS" ] && [ "$revision" = "10" ]; then
                echo "✓ PERFECT: Config contains expected values"
                echo "✓ Our function should return revision '$revision' as the serial number"
            else
                echo "⚠ Config values don't match expected MASTER_IRIS/10"
            fi
        else
            echo "✗ Target 0 is not enabled"
        fi
    else
        echo "✗ Invalid SCSI2SD config"
    fi
    
    rm -f "$config_file"
else
    echo "✗ Failed to read config file"
fi

echo
echo "=== Test Complete ==="
