#!/bin/bash

# Test script to convert SCSI2SD XML config to binary and write to SD card

echo "=== SCSI2SD XML to Binary Config Writer Test ==="
echo

# Configuration
XML_FILE="AT2635_Dash_Upper-IOS_SN12_SCSI2SD_Settings.xml"
TEST_MODE=true  # Set to false to actually write to SD card

# Source the functions
source usr/sbin/ocs-flyright-functions

echo "XML file: $XML_FILE"
echo "Test mode: $TEST_MODE"
echo

# Find the UHS-II slot device
echo "=== Finding UHS-II Slot Device ==="
device=$(detect_uhs2_slot)

if [ $? -eq 0 ] && [ -n "$device" ]; then
    echo "✓ Found UHS-II slot device: $device"
else
    echo "✗ UHS-II slot not found"
    exit 1
fi

echo "Requesting sudo access..."
sudo -v

echo
echo "=== Parsing XML Configuration ==="

if [ ! -f "$XML_FILE" ]; then
    echo "✗ XML file not found: $XML_FILE"
    exit 1
fi

echo "✓ XML file found: $XML_FILE"

# Function to parse XML value
parse_xml_value() {
    local xml_file="$1"
    local xpath="$2"
    
    # Simple XML parsing using grep and sed
    grep -o "<$xpath>.*</$xpath>" "$xml_file" | sed "s/<$xpath>//g" | sed "s/<\/$xpath>//g" | head -1
}

# Function to parse XML value for specific target
parse_xml_target_value() {
    local xml_file="$1"
    local target_id="$2"
    local field="$3"
    
    # Extract the target section and then parse the field
    sed -n "/<SCSITarget id=\"$target_id\">/,/<\/SCSITarget>/p" "$xml_file" | \
    grep -o "<$field>.*</$field>" | sed "s/<$field>//g" | sed "s/<\/$field>//g" | head -1
}

echo
echo "=== Board Configuration ==="

# Parse board config
enable_terminator=$(parse_xml_value "$XML_FILE" "enableTerminator")
unit_attention=$(parse_xml_value "$XML_FILE" "unitAttention")
parity=$(parse_xml_value "$XML_FILE" "parity")
enable_scsi2=$(parse_xml_value "$XML_FILE" "enableScsi2")
sel_latch=$(parse_xml_value "$XML_FILE" "selLatch")
map_luns_to_ids=$(parse_xml_value "$XML_FILE" "mapLunsToIds")
selection_delay=$(parse_xml_value "$XML_FILE" "selectionDelay")
startup_delay=$(parse_xml_value "$XML_FILE" "startupDelay")
scsi_speed=$(parse_xml_value "$XML_FILE" "scsiSpeed")

echo "Enable Terminator: $enable_terminator"
echo "Unit Attention: $unit_attention"
echo "Parity: $parity"
echo "Enable SCSI2: $enable_scsi2"
echo "Selection Latch: $sel_latch"
echo "Map LUNs to IDs: $map_luns_to_ids"
echo "Selection Delay: $selection_delay"
echo "Startup Delay: $startup_delay"
echo "SCSI Speed: $scsi_speed"

echo
echo "=== Target 0 Configuration ==="

# Parse target 0 config (the enabled one)
target0_enabled=$(parse_xml_target_value "$XML_FILE" "0" "enabled")
target0_device_type=$(parse_xml_target_value "$XML_FILE" "0" "deviceType")
target0_device_type_modifier=$(parse_xml_target_value "$XML_FILE" "0" "deviceTypeModifier")
target0_sd_sector_start=$(parse_xml_target_value "$XML_FILE" "0" "sdSectorStart")
target0_scsi_sectors=$(parse_xml_target_value "$XML_FILE" "0" "scsiSectors")
target0_bytes_per_sector=$(parse_xml_target_value "$XML_FILE" "0" "bytesPerSector")
target0_sectors_per_track=$(parse_xml_target_value "$XML_FILE" "0" "sectorsPerTrack")
target0_heads_per_cylinder=$(parse_xml_target_value "$XML_FILE" "0" "headsPerCylinder")
target0_vendor=$(parse_xml_target_value "$XML_FILE" "0" "vendor")
target0_prod_id=$(parse_xml_target_value "$XML_FILE" "0" "prodId")
target0_revision=$(parse_xml_target_value "$XML_FILE" "0" "revision")
target0_serial=$(parse_xml_target_value "$XML_FILE" "0" "serial")

echo "Enabled: $target0_enabled"
echo "Device Type: $target0_device_type"
echo "Device Type Modifier: $target0_device_type_modifier"
echo "SD Sector Start: $target0_sd_sector_start"
echo "SCSI Sectors: $target0_scsi_sectors"
echo "Bytes Per Sector: $target0_bytes_per_sector"
echo "Sectors Per Track: $target0_sectors_per_track"
echo "Heads Per Cylinder: $target0_heads_per_cylinder"
echo "Vendor: '$target0_vendor'"
echo "Product ID: '$target0_prod_id'"
echo "Revision: '$target0_revision'"
echo "Serial: '$target0_serial'"

echo
echo "=== Creating Binary Configuration ==="

# Create temporary file for binary config
config_file="/tmp/scsi2sd_new_config.bin"

# Initialize 1024-byte config with zeros
dd if=/dev/zero of="$config_file" bs=1024 count=1 2>/dev/null

echo "✓ Created empty 1024-byte config file"

# Function to write little-endian 32-bit value
write_le32() {
    local file="$1"
    local offset="$2"
    local value="$3"

    printf "\\$(printf '%03o' $((value & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$offset conv=notrunc 2>/dev/null
    printf "\\$(printf '%03o' $(((value >> 8) & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$((offset + 1)) conv=notrunc 2>/dev/null
    printf "\\$(printf '%03o' $(((value >> 16) & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$((offset + 2)) conv=notrunc 2>/dev/null
    printf "\\$(printf '%03o' $(((value >> 24) & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$((offset + 3)) conv=notrunc 2>/dev/null
}

# Function to write little-endian 16-bit value
write_le16() {
    local file="$1"
    local offset="$2"
    local value="$3"

    printf "\\$(printf '%03o' $((value & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$offset conv=notrunc 2>/dev/null
    printf "\\$(printf '%03o' $(((value >> 8) & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$((offset + 1)) conv=notrunc 2>/dev/null
}

# Function to write 8-bit value
write_u8() {
    local file="$1"
    local offset="$2"
    local value="$3"

    printf "\\$(printf '%03o' $((value & 0xFF)))" | dd of="$file" bs=1 count=1 seek=$offset conv=notrunc 2>/dev/null
}

# Function to write string with padding
write_string() {
    local file="$1"
    local offset="$2"
    local string="$3"
    local max_length="$4"

    # Truncate or pad string to exact length
    local padded_string=$(printf "%-${max_length}s" "$string" | cut -c1-$max_length)

    # Write each character
    for ((i=0; i<max_length; i++)); do
        local char="${padded_string:$i:1}"
        if [ -z "$char" ]; then
            char=" "
        fi
        printf "$char" | dd of="$file" bs=1 count=1 seek=$((offset + i)) conv=notrunc 2>/dev/null
    done
}

echo "=== Writing Board Configuration (128 bytes) ==="

# Board config starts at offset 0
# S2S_BoardCfg structure:
# char magic[4];           // offset 0
# uint8_t flags;           // offset 4
# uint8_t startupDelay;    // offset 5
# uint8_t selectionDelay;  // offset 6
# uint8_t flags6;          // offset 7
# uint8_t scsiSpeed;       // offset 8
# uint8_t reserved[119];   // offset 9-127

# Write magic "BCFG"
printf "BCFG" | dd of="$config_file" bs=1 count=4 seek=0 conv=notrunc 2>/dev/null

# Calculate flags byte
flags=0
[ "$unit_attention" = "true" ] && flags=$((flags | 1))
[ "$parity" = "true" ] && flags=$((flags | 2))
[ "$enable_scsi2" = "true" ] && flags=$((flags | 4))
[ "$sel_latch" = "true" ] && flags=$((flags | 64))
[ "$map_luns_to_ids" = "true" ] && flags=$((flags | 128))

write_u8 "$config_file" 4 $flags
write_u8 "$config_file" 5 $startup_delay
write_u8 "$config_file" 6 $selection_delay

# Calculate flags6 byte
flags6=0
[ "$enable_terminator" = "true" ] && flags6=$((flags6 | 1))

write_u8 "$config_file" 7 $flags6
write_u8 "$config_file" 8 $scsi_speed

echo "✓ Board configuration written"

echo "=== Writing Target 0 Configuration (128 bytes) ==="

# Target config starts at offset 128
# S2S_TargetCfg structure:
# uint8_t scsiId;              // offset 0
# uint8_t deviceType;          // offset 1
# uint8_t flagsDEPRECATED;     // offset 2
# uint8_t deviceTypeModifier;  // offset 3
# uint32_t sdSectorStart;      // offset 4
# uint32_t scsiSectors;        // offset 8
# uint16_t bytesPerSector;     // offset 12
# uint16_t sectorsPerTrack;    // offset 14
# uint16_t headsPerCylinder;   // offset 16
# char vendor[8];              // offset 18
# char prodId[16];             // offset 26
# char revision[4];            // offset 42
# char serial[16];             // offset 46
# uint16_t quirks;             // offset 62
# uint8_t reserved[64];        // offset 64-127

target_offset=128

# SCSI ID with enabled flag
scsi_id=0  # Target 0
if [ "$target0_enabled" = "true" ]; then
    scsi_id=$((scsi_id | 0x80))  # Set enabled bit
fi

write_u8 "$config_file" $target_offset $scsi_id

# Convert hex device type
device_type_val=$(printf "%d" "$target0_device_type")
write_u8 "$config_file" $((target_offset + 1)) $device_type_val

# flagsDEPRECATED (always 0)
write_u8 "$config_file" $((target_offset + 2)) 0

# Device type modifier
device_type_mod_val=$(printf "%d" "$target0_device_type_modifier")
write_u8 "$config_file" $((target_offset + 3)) $device_type_mod_val

# SD sector start (32-bit little endian)
write_le32 "$config_file" $((target_offset + 4)) $target0_sd_sector_start

# SCSI sectors (32-bit little endian)
write_le32 "$config_file" $((target_offset + 8)) $target0_scsi_sectors

# Bytes per sector (16-bit little endian)
write_le16 "$config_file" $((target_offset + 12)) $target0_bytes_per_sector

# Sectors per track (16-bit little endian)
write_le16 "$config_file" $((target_offset + 14)) $target0_sectors_per_track

# Heads per cylinder (16-bit little endian)
write_le16 "$config_file" $((target_offset + 16)) $target0_heads_per_cylinder

# String fields
write_string "$config_file" $((target_offset + 18)) "$target0_vendor" 8
write_string "$config_file" $((target_offset + 26)) "$target0_prod_id" 16
write_string "$config_file" $((target_offset + 42)) "$target0_revision" 4
write_string "$config_file" $((target_offset + 46)) "$target0_serial" 16

# Quirks (16-bit little endian, default 0)
write_le16 "$config_file" $((target_offset + 62)) 0

echo "✓ Target 0 configuration written"

echo
echo "=== Verifying Created Configuration ==="

# Verify magic signature
magic_check=$(dd if="$config_file" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')
echo "Magic signature: $magic_check"

if [ "$magic_check" = "42434647" ]; then
    echo "✓ Valid magic signature"
else
    echo "✗ Invalid magic signature"
fi

# Read back target 0 fields for verification
echo
echo "Target 0 verification:"

# Read SCSI ID
scsi_id_check=$(dd if="$config_file" bs=1 count=1 skip=128 2>/dev/null | od -tu1 -An | tr -d ' ')
echo "SCSI ID byte: $scsi_id_check (enabled: $((scsi_id_check & 0x80 ? 1 : 0)))"

# Read vendor
vendor_check=$(dd if="$config_file" bs=8 count=1 skip=$((128 + 18)) 2>/dev/null | tr -d '\0')
echo "Vendor: '$vendor_check'"

# Read product ID
product_check=$(dd if="$config_file" bs=16 count=1 skip=$((128 + 26)) 2>/dev/null | tr -d '\0')
echo "Product ID: '$product_check'"

# Read revision
revision_check=$(dd if="$config_file" bs=4 count=1 skip=$((128 + 42)) 2>/dev/null | tr -d '\0')
echo "Revision: '$revision_check'"

# Read serial
serial_check=$(dd if="$config_file" bs=16 count=1 skip=$((128 + 46)) 2>/dev/null | tr -d '\0')
echo "Serial: '$serial_check'"

echo
if [ "$TEST_MODE" = "true" ]; then
    echo "=== TEST MODE - Configuration Not Written to SD Card ==="
    echo "Created config file: $config_file"
    echo "To actually write to SD card, set TEST_MODE=false"
    echo
    echo "Config file size: $(stat -c%s "$config_file") bytes"
    echo "You can examine the binary config with: hexdump -C $config_file"
else
    echo "=== Writing Configuration to SD Card ==="

    # Get device size and calculate config location
    device_size=$(sudo blockdev --getsize64 "$device")
    device_sectors=$((device_size / 512))
    config_start_sector=$((device_sectors - 2))

    echo "Device: $device"
    echo "Device size: $device_size bytes"
    echo "Config location: sectors $config_start_sector-$((device_sectors - 1))"

    echo "Writing config to SD card..."
    sudo dd if="$config_file" of="$device" bs=512 count=2 seek=$config_start_sector conv=fsync 2>/dev/null

    if [ $? -eq 0 ]; then
        echo "✓ Configuration successfully written to SD card"

        # Verify by reading back
        echo "Verifying written config..."
        verify_file="/tmp/scsi2sd_verify.bin"
        sudo dd if="$device" of="$verify_file" bs=512 count=2 skip=$config_start_sector 2>/dev/null

        if cmp -s "$config_file" "$verify_file"; then
            echo "✓ Verification successful - config matches"
        else
            echo "✗ Verification failed - config mismatch"
        fi

        rm -f "$verify_file"
    else
        echo "✗ Failed to write configuration to SD card"
    fi
fi

# Clean up
rm -f "$config_file"

echo
echo "=== Test Complete ==="
