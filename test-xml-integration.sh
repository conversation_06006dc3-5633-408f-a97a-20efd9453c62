#!/bin/bash

# Test script for XML config integration in main backup script

echo "=== Testing XML Config Integration ==="
echo

# Source the functions
source usr/sbin/ocs-flyright-functions

# Test the XML parsing functions
xml_file="AT2635_Dash_Upper-IOS_SN12_SCSI2SD_Settings.xml"

if [ ! -f "$xml_file" ]; then
    echo "✗ XML test file not found: $xml_file"
    exit 1
fi

echo "✓ XML test file found: $xml_file"

echo
echo "=== Testing XML Parsing Functions ==="

# Test parse_xml_value function
echo "Testing parse_xml_value function..."
terminator=$(parse_xml_value "$xml_file" "enableTerminator")
echo "Enable Terminator: '$terminator'"

selection_delay=$(parse_xml_value "$xml_file" "selectionDelay")
echo "Selection Delay: '$selection_delay'"

# Test parse_xml_target_value function
echo
echo "Testing parse_xml_target_value function..."
target0_enabled=$(parse_xml_target_value "$xml_file" "0" "enabled")
echo "Target 0 Enabled: '$target0_enabled'"

target0_vendor=$(parse_xml_target_value "$xml_file" "0" "vendor")
echo "Target 0 Vendor: '$target0_vendor'"

target0_prod_id=$(parse_xml_target_value "$xml_file" "0" "prodId")
echo "Target 0 Product ID: '$target0_prod_id'"

target0_revision=$(parse_xml_target_value "$xml_file" "0" "revision")
echo "Target 0 Revision: '$target0_revision'"

target0_serial=$(parse_xml_target_value "$xml_file" "0" "serial")
echo "Target 0 Serial: '$target0_serial'"

echo
echo "=== Testing Binary Creation Functions ==="

# Test binary creation
test_config_file="/tmp/test_xml_config.bin"
echo "Creating test binary config file: $test_config_file"

# Initialize with zeros
dd if=/dev/zero of="$test_config_file" bs=1024 count=1 2>/dev/null

# Test the main parsing function
echo "Testing parse_xml_and_create_binary function..."
if parse_xml_and_create_binary "$xml_file" "$test_config_file"; then
    echo "✓ Binary config creation successful"
    
    # Verify the created config
    echo
    echo "=== Verifying Created Binary Config ==="
    
    # Check magic signature
    magic=$(dd if="$test_config_file" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')
    echo "Magic signature: $magic"
    
    if [ "$magic" = "42434647" ]; then
        echo "✓ Valid magic signature (BCFG)"
    else
        echo "✗ Invalid magic signature"
    fi
    
    # Check target 0 configuration
    target_offset=128
    scsi_id_byte=$(dd if="$test_config_file" bs=1 count=1 skip=$target_offset 2>/dev/null | od -tu1 -An | tr -d ' ')
    echo "Target 0 SCSI ID byte: $scsi_id_byte (enabled: $((scsi_id_byte & 0x80 ? 1 : 0)))"
    
    # Read back string fields
    vendor_check=$(dd if="$test_config_file" bs=1 count=8 skip=$((target_offset + 18)) 2>/dev/null | tr -d '\0')
    echo "Vendor: '$vendor_check'"
    
    product_check=$(dd if="$test_config_file" bs=1 count=16 skip=$((target_offset + 26)) 2>/dev/null | tr -d '\0')
    echo "Product ID: '$product_check'"
    
    revision_check=$(dd if="$test_config_file" bs=1 count=4 skip=$((target_offset + 42)) 2>/dev/null | tr -d '\0')
    echo "Revision: '$revision_check'"
    
    serial_check=$(dd if="$test_config_file" bs=1 count=16 skip=$((target_offset + 46)) 2>/dev/null | tr -d '\0')
    echo "Serial: '$serial_check'"
    
    echo
    echo "=== Binary Config Hexdump (first 256 bytes) ==="
    hexdump -C "$test_config_file" | head -16
    
else
    echo "✗ Binary config creation failed"
fi

# Clean up
rm -f "$test_config_file"

echo
echo "=== Testing Menu Functions ==="

# Test XML file discovery
xml_dir="/home/<USER>"
echo "Looking for XML files in $xml_dir..."

if [ -d "$xml_dir" ]; then
    xml_count=$(find "$xml_dir" -name "*.xml" -type f 2>/dev/null | wc -l)
    echo "Found $xml_count XML files in $xml_dir"
    
    if [ $xml_count -gt 0 ]; then
        echo "XML files found:"
        find "$xml_dir" -name "*.xml" -type f 2>/dev/null | while read xml; do
            echo "  - $(basename "$xml")"
        done
    else
        echo "⚠ No XML files found in $xml_dir"
        echo "For testing, you may want to copy some XML files there"
    fi
else
    echo "⚠ Directory $xml_dir does not exist"
    echo "This directory should contain SCSI2SD XML configuration files"
fi

echo
echo "=== Integration Test Summary ==="
echo "✓ XML parsing functions working"
echo "✓ Binary creation functions working"
echo "✓ File discovery functions working"
echo
echo "The integration should work when:"
echo "1. A blank SD card is detected (no SCSI2SD config)"
echo "2. User selects backup operation"
echo "3. Script shows blank SD card menu"
echo "4. User selects 'Write Config'"
echo "5. Script shows XML file selection menu"
echo "6. User selects an XML file"
echo "7. Script writes config and restarts SD card selection"

echo
echo "=== Test Complete ==="
