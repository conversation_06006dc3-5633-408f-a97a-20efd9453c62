#!/bin/bash
#
# ocs-flyright-functions - Support functions for Flyright Aviation Clonezilla system
#
# Description:
#   This file contains utility functions used by the main ocs-flyright script.
#   It provides database interaction, user interface functions, drive operations,
#   and logging capabilities specifically designed for Flyright Aviation's
#   simulator drive management workflow.
#
# Functions included:
#   - Database interaction (select_simulator, select_computer, select_at)
#   - Drive management (add_row, update_row, add_or_modify_drive_to_DB)
#   - Backup/restore operations (backup_standard_drive_action, create_restore_command)
#   - Image selection (get_restore_img with smart sorting)
#   - Logging (log_drive_action with database integration)
#   - SD card operations (SD_drive_action with UHS-II optimization)
#   - SCSI2SD configuration parsing and handling
#   - Smart backup sizing based on previous backups or device configuration
#   - Progress monitoring with pv and mbuffer integration
#

# ============================================================================
# CONFIGURATION
# ============================================================================

# Database path for drive information
db_path="/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"

echo "Starting ocs-flyright-functions"

# Dialog interface tool
DIA="whiptail"

# Path to Clonezilla's ocs-sr command
ocs_sr="/home/<USER>/Desktop/clonezilla-master/usr/sbin/ocs-sr"

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

#
# error_exit() - Display error message and exit
#
# Parameters:
#   $1 - Error message to display
#
# Description:
#   Shows an error dialog using whiptail and exits the script
#
error_exit() {
    whiptail --title "Error" --msgbox "$1" 8 50
    exit 1
}



# ============================================================================
# DATABASE INTERACTION FUNCTIONS
# ============================================================================

#
# select_simulator() - Present menu to select a simulator from database
#
# Returns:
#   Echoes: Selected simulator name
#   Exit code: 0 on success, 1 on error/cancel
#
# Description:
#   Queries the database for available simulators and presents them in a
#   whiptail menu for user selection. Handles database errors and user cancellation.
#
select_simulator() {
    # Check if database exists
    if [ ! -f "$db_path" ]; then
        error_exit "Database file $db_path not found."
        return 1
    fi

    local simulators=$(sqlite3 "$db_path" "SELECT DISTINCT Simulator FROM drives;" 2>/dev/null | sed 's/\"/\\\"/g')

    # Check if database query was successful
    if [ $? -ne 0 ]; then
        error_exit "Database query failed."
        return 1
    fi

    local sim_menu=""

    # Build the whiptail menu for simulators
    while IFS= read -r sim; do
        if [ -n "$sim" ]; then
            sim_menu+="\"$sim\" \"$sim\" "
        fi
    done <<< "$simulators"

    if [[ -z "$sim_menu" ]]; then
        error_exit "No simulators found in the database."
        return 1
    fi

    local selected_sim=$(eval "whiptail --title \"Select Simulator\" --menu \"Choose a Simulator:\" 15 50 8 $sim_menu 3>&1 1>&2 2>&3")
    if [[ $? -ne 0 || -z "$selected_sim" ]]; then
        error_exit "Operation canceled by user."
        return 1
    fi

    echo "$selected_sim"
}

#
# select_computer() - Present menu to select a computer for given simulator
#
# Parameters:
#   $1 - Simulator name to filter computers by
#
# Returns:
#   Echoes: Selected computer name
#   Exit code: 0 on success, 1 on error/cancel
#
# Description:
#   Queries the database for computers associated with the specified simulator
#   and presents them in a whiptail menu. Multiple drives with the same computer
#   name are treated as one entry.
#
select_computer() {
    local simulator="$1"

    # Validate input parameter
    if [ -z "$simulator" ]; then
        error_exit "No simulator specified."
        return 1
    fi

    local computers=$(sqlite3 "$db_path" "SELECT DISTINCT Computer FROM drives WHERE Simulator = '$simulator';" 2>/dev/null | sed 's/\"/\\\"/g')

    # Check if database query was successful
    if [ $? -ne 0 ]; then
        error_exit "Database query failed."
        return 1
    fi

    local comp_menu=""

    # Build the whiptail menu for computers
    while IFS= read -r comp; do
        if [ -n "$comp" ]; then
            comp_menu+="\"$comp\" \"$comp\" "
        fi
    done <<< "$computers"

    if [[ -z "$comp_menu" ]]; then
        error_exit "No computers found for Simulator '$simulator'."
        return 1
    fi

    local selected_comp=$(eval "whiptail --title \"Select Computer\" --menu \"Choose a Computer:\" 15 50 8 $comp_menu 3>&1 1>&2 2>&3")

    if [[ $? -ne 0 || -z "$selected_comp" ]]; then
        error_exit "Operation canceled by user."
        return 1
    fi

    echo "$selected_comp"
}

select_at() {
    local simulator="$1"
	local computers="$2"
    local asset_tags=$(sqlite3 "$db_path" "SELECT DISTINCT \"Asset Tag\" FROM drives WHERE Simulator = '$simulator' AND Computer = '$computers';" | sed 's/\"/\\\"/g')
    local tag_menu=""


    # Build the whiptail menu for computers
    while IFS= read -r tag; do
        tag_menu+="\"$tag\" \"$tag\" ";
    done <<< "$asset_tags"

    if [[ -z "$tag_menu" ]]; then
        error_exit "No Asset Tags found for '$simulator' '$computers'."
		
    fi

    local selected_tag=$(eval "whiptail --title \"Select Asset Tag\" --menu \"Choose an Asset Tag:\" 15 50 8 $tag_menu 3>&1 1>&2 2>&3")

    if [[ $? -ne 0 || -z "$selected_tag" ]]; then
        error_exit "Operation canceled by user."
    fi

    echo "$selected_tag"
}

# Helper function to select Letter/PriSec (reduces duplication)
select_letter_prisec() {
    local letter_prisec=$(whiptail --title "Select Letter/PriSec" \
    --menu "Choose a Letter/PriSec value:" 15 50 8 \
    "Primary" "" \
    "Secondary" "" \
    "Tertiary" "" \
    "A" "" \
    "B" "" \
    "C" "" \
    "D" "" \
    3>&1 1>&2 2>&3)

    if [[ $? -ne 0 || -z "$letter_prisec" ]]; then
        error_exit "Operation canceled by user."
        return 1
    fi

    echo "$letter_prisec"
}


# Function to add or update a row
add_row() {
    local simulator="$1"
    local computer="$2"
    local newdrive="$3"

    # Get Letter/PriSec
    local letter_prisec=$(select_letter_prisec)
    if [ $? -ne 0 ]; then
        return 1
    fi

    # Get Asset Tag
local asset_tag=$(whiptail --title "Enter Asset Tag" \
    --inputbox "Enter Asset Tag value. If you have not already made an Asset Tag in Salesforce, go do that now.\nDo not leave this blank.\nTag should be 6 digits long: AT-000000\nMake sure you type the AT- as well." \
    12 50 3>&1 1>&2 2>&3)
    if [[ $? -ne 0 || -z "$asset_tag" ]]; then
        error_exit "Operation canceled by user."
    fi

    # Get Serial Number
    local serial_number=$(sudo hdparm -I "$newdrive" | grep "Serial Number" | awk '{print $3}')
  # Insert or update the row in the database
existing_row=$(sqlite3 "$db_path" "SELECT COUNT(*) FROM drives WHERE Simulator = '$simulator' AND 'Letter/PriSec' = '$letter_prisec' AND 'Asset Tag' = '$asset_tag'")

whiptail --title "Confirm Update" \
    --yesno "Are you sure you want to add this drive to the database?\n\n$computer\n$simulator\n$letter_prisec\n$asset_tag\n$serial_number" \
    15 50 3>&1 1>&2 2>&3
confirmupdate=$?
echo $confirmupdate	
if [[ $confirmupdate -eq 0 ]]; then
    sqlite3 "$db_path" "INSERT INTO drives (Computer, Simulator, 'Letter/PriSec', 'Asset Tag', 'Serial Number', 'Revision Number') VALUES ('$computer', '$simulator', '$letter_prisec', '$asset_tag', '$serial_number', 0);"
    whiptail --title "Success" --msgbox "Row successfully Added." 8 50
else
    error_exit "Operation canceled by user."
fi
}
# Function to add or update a row
update_row() {
    local simulator="$1"
    local computer="$2"
	local asset_tag="$3"
    local newdrive="$4"

    # Get Letter/PriSec
    local letter_prisec=$(select_letter_prisec)
    if [ $? -ne 0 ]; then
        return 1
    fi
    # Get Serial Number
    local serial_number=$(sudo hdparm -I "$newdrive" | grep "Serial Number" | awk '{print $3}')
  # Insert or update the row in the database
existing_row=$(sqlite3 "$db_path" "SELECT COUNT(*) FROM drives WHERE Simulator = '$simulator' AND 'Letter/PriSec' = '$letter_prisec' AND 'Asset Tag' = '$asset_tag'")

whiptail --title "Confirm Update" \
    --yesno "Are you sure you want to add this drive to the database?\n\n$computer\n$simulator\n$letter_prisec\n$asset_tag\n$serial_number\n$newdrive" \
    15 50 3>&1 1>&2 2>&3
confirmupdate=$?
echo $confirmupdate	
if [[ "$confirmupdate" -eq 0 ]]; then
    sqlite3 "$db_path" "UPDATE drives SET 'Asset Tag' = '$asset_tag', 'Serial Number' = '$serial_number', 'Revision Number' = COALESCE('Revision Number', 0) WHERE Simulator = '$simulator' AND 'Letter/PriSec' = '$letter_prisec';"
    whiptail --title "Success" --msgbox "Row successfully updated." 8 50
else
    error_exit "Operation canceled by user."
fi
}
add_or_modify_drive_to_DB() {
    local newdrive="$1"
    echo "target drive sent is \"$1\""
    echo "add or modify"
    local TMP="$(mktemp /tmp/menu.XXXXXX)"
    trap "[ -f \"$TMP\" ] && rm -f \"$TMP\"" HUP INT QUIT TERM EXIT
    echo "$DIA"
    $DIA --backtitle "$msg_nchc_clonezilla" --title \
    "Drive Not found in database" --menu "Choose whether to add this drive \"$newdrive\" as a new drive or to modify an existing drive." 20 50 0 \
    "Add new drive" "Add this drive as a brand new drive with a brand new Asset tag" \
    "Modify existing Drive" "Change the serial number of an existing drive in the Database" \
    2> "$TMP"
    local dmode="$(cat "$TMP")"
    [ -f "$TMP" ] && rm -f "$TMP"

    case "$dmode" in
        "Add new drive")
            add_new_drive "$newdrive"
            ;;
        "Modify existing Drive")
            modify_existing_drive "$newdrive"
            ;;
        *)
            [ "$BOOTUP" = "color" ] && $SETCOLOR_FAILURE
            echo "Program terminated!"
            [ "$BOOTUP" = "color" ] && $SETCOLOR_NORMAL
            exit 1
            ;;
    esac
}

add_new_drive() {
    local newdrive="$1"
    local simulator=$(select_simulator)
    local computer=$(select_computer "$simulator")
    add_row "$simulator" "$computer" "$newdrive"
}

modify_existing_drive() {
    local newdrive="$1"
    local simulator=$(select_simulator)
    local computer=$(select_computer "$simulator")
    local at=$(select_at "$simulator" "$computer")
    update_row "$simulator" "$computer" "$at" "$newdrive"
}

backup_standard_drive_action() {
    local optype="$1"
    local target_dev="$2"
    local backupname="$3"
    local rev_num="$4"
    local at="$5"
    local serial_number="$6"
    local simulator="$7"
    local computer="$8"

    if [ "$optype" = "savedisk" ]; then
        $ocs_sr -q2 -c -j2 -z1p -i 4096 -sfsck -scs -senc -p true "$optype" "$backupname" "$target_dev"

        if [ $? = "0" ]; then
            local cmd="UPDATE drives SET \"Revision Number\" = '$rev_num' WHERE \"Asset Tag\" = '$at' AND \"Serial Number\" = '$serial_number';"
            sqlite3 "$db_path" "$cmd"
            echo "revision number set for \"$at\" rev num \"$rev_num\""

            # Check and update sibling drives after successful backup
            check_and_update_sibling_drives "$serial_number" "$rev_num" "$db_path"
        else
            echo "ocs-sr did not return zero, did not modify rev number in database"
        fi
    fi

}


create_restore_command() {

local backupname=$1
local target_dev=$2
local rev=$3
local at=$4
local serial_number=$5
local drivetype=$6
if [[ "$drivetype" == "HDD" ]]; then
$ocs_sr -q2 -c -j2 -z1p -i 4096 -sfsck --skip-check-restorable-s -senc -p true restoredisk "$backupname" "$target_dev"
else
SD_drive_action "restoredisk" "$target_dev" "$backupname" "$rev" "$at" "$serial_number"
fi
	    if [ $? = "0" ]; then
        cmd="UPDATE drives SET \"Revision Number\" = '$rev' WHERE \"Asset Tag\" = '$at' AND \"Serial Number\" = '$serial_number';"
	    sqlite3 "$db_path" "$cmd"
        echo revision number set for "$at" rev num "$rev"
	    else
	    echo "ocs-sr did not return zero, did not modify rev number in database"
	    fi

}

# Function to check sibling drives and mark them for update if needed
check_and_update_sibling_drives() {
    local current_serial_number="$1"
    local current_rev_number="$2"
    local db_path="$3"

    echo "Checking sibling drives for updates..."

    # Get the sibling drives field for the current drive
    local sibling_drives=$(sqlite3 "$db_path" <<EOF
.header off
.mode list
SELECT "Sibling Drives" FROM Drives WHERE "Serial Number" = '$current_serial_number';
EOF
    )

    # Check if there are any siblings
    if [[ -z "$sibling_drives" || "$sibling_drives" == "" ]]; then
        echo "No sibling drives found for serial number: $current_serial_number"
        return 0
    fi

    echo "Found sibling drives: $sibling_drives"

    # Split the sibling drives by comma and iterate through each
    IFS=', ' read -ra SIBLING_ARRAY <<< "$sibling_drives"
    for sibling_uuid in "${SIBLING_ARRAY[@]}"; do
        # Trim whitespace
        sibling_uuid=$(echo "$sibling_uuid" | xargs)

        if [[ -n "$sibling_uuid" ]]; then
            echo "Checking sibling UUID: $sibling_uuid"

            # Get the revision number of the sibling drive
            local sibling_rev=$(sqlite3 "$db_path" <<EOF
.header off
.mode list
SELECT COALESCE("Revision Number", 0) FROM Drives WHERE UUID = '$sibling_uuid';
EOF
            )

            echo "Sibling UUID $sibling_uuid has revision: $sibling_rev, current drive has revision: $current_rev_number"

            # Compare revision numbers - if current drive has higher revision, mark sibling for update
            if [[ -n "$sibling_rev" && "$current_rev_number" -gt "$sibling_rev" ]]; then
                echo "Marking sibling UUID $sibling_uuid for update (revision $sibling_rev < $current_rev_number)"

                # Update the "Update Needed" field for the sibling drive
                sqlite3 "$db_path" <<EOF
UPDATE Drives SET "Update Needed" = 'true' WHERE UUID = '$sibling_uuid';
EOF

                if [[ $? -eq 0 ]]; then
                    echo "Successfully marked sibling UUID $sibling_uuid for update"
                else
                    echo "Error: Failed to update sibling UUID $sibling_uuid"
                fi
            else
                echo "Sibling UUID $sibling_uuid does not need update (revision $sibling_rev >= $current_rev_number)"
            fi
        fi
    done
}

# Function to handle adding a restored SD card to the database
# This is called after a successful restore operation on a blank SD card
handle_restored_sd_card_database_addition() {
    local target_dev="$1"
    local new_serial_number="$2"

    echo "Handling database addition for restored SD card..."
    echo "Device: $target_dev"
    echo "New serial number: $new_serial_number"

    # Check if this serial number already exists in database
    local existing_check=$(sqlite3 "$database_path" <<EOF
.header off
.mode list
SELECT "Serial Number" FROM Drives WHERE "Serial Number" = '$new_serial_number';
EOF
    )

    if [ -n "$existing_check" ]; then
        echo "Serial number $new_serial_number already exists in database."
        whiptail --title "Serial Number Exists" --msgbox "The serial number $new_serial_number already exists in the database.\n\nNo action needed." 8 60
        return 0
    fi

    # Serial number not in database, offer to add it
    whiptail --backtitle "$msg_nchc_free_software_labs" --title "Add Restored SD Card to Database" \
        --yesno "The SD card restore is complete and a new serial number has been detected:\n\n$new_serial_number\n\nThis serial number is not in the database. Would you like to add this drive to the database now?\n\nNote: You will be prompted to select the simulator, computer, and other details." 12 80

    if [ $? -eq 0 ]; then
        echo "User chose to add restored SD card to database..."

        # Use the existing add_or_modify_drive_to_DB function
        # But we need to temporarily set the target_serial_number for the function to work
        local original_target_serial_number="$target_serial_number"
        target_serial_number="$new_serial_number"

        add_or_modify_drive_to_DB "$target_dev"
        local add_result=$?

        # Restore original value
        target_serial_number="$original_target_serial_number"

        if [ $add_result -eq 0 ]; then
            echo "Successfully added restored SD card to database."
            return 0
        else
            echo "Failed to add restored SD card to database."
            return 1
        fi
    else
        echo "User chose to skip database addition for restored SD card."
        whiptail --title "Database Addition Skipped" --msgbox "The restored SD card will not be added to the database.\n\nYou can add it later using the database management tools." 8 60
        return 0
    fi
}

log_drive_action(){
	local optype=$1
	local target_dev=$2
	local backupname=$3
	local rev_num=$4
	local at=$5
	local serial_number=$6
    local simulator=$7
    local computer=$8
	local letter_prisec=$9
	local date=${10}

    if [[ "$optype" == "restoredisk" ]]; then
    op="Restore"
    else
    op="Backup"
    fi

    local baselogpath="/home/<USER>/Desktop/DriveDatabase/simulator_databases"
    local log_path="$baselogpath/$simulator/$computer.db"

    local log_msg=$(whiptail --title "Enter Log Comments" \
        --inputbox "Enter log comments. Describe relevant info, such as software changes,\nthat preceded this backup or restore." \
        12 50 3>&1 1>&2 2>&3)

    if [[ $? -ne 0 || -z "$log_msg" ]]; then
        error_exit "Operation canceled by user."
    fi

    sqlite3 "$log_path" "INSERT INTO logs ('Letter/PriSec', 'Asset Tag', 'Serial Number', 'Log Comment', 'Action', 'Revision Number', 'Date') VALUES ('$letter_prisec', '$at', '$serial_number', '$log_msg', '$op', '$rev_num', '$date');"
    whiptail --title "Success" --msgbox "Row successfully Added." 8 50

}


get_restore_img() {
    # Parameters
    local simulator_input=$1
    local computer_input=$2
    local drivetype=$3
    
    echo "DEBUG: get_restore_img called with: $simulator_input, $computer_input, $drivetype" >&2
    
    # Variables to track highest revision
    local highest_revision=0
    local highest_dir=""

    # Gather matching subdirectories
    local subdirs=()
    
    # Search pattern depends on drive type
    local search_pattern="/home/<USER>/${simulator_input}-${computer_input}-*-*-img"
    echo "DEBUG: Searching with pattern: $search_pattern" >&2
    
    # Gather matching backup files or directories based on drive type
    for subdir in $search_pattern; do
        # Check based on drive type
        if [[ "$drivetype" == "SD" ]]; then
            # For SD cards, look for backup files
            if [[ ! -f "$subdir" ]]; then
                continue
            fi
            subdir=$(basename "$subdir")
            echo "DEBUG: Found SD backup file: $subdir" >&2
        else
            # For HDD drives, look for backup directories
            if [[ ! -d "$subdir" ]]; then
                continue
            fi
            subdir=$(basename "$subdir")
            echo "DEBUG: Found HDD backup directory: $subdir" >&2
        fi

        # Extract components using '-'
        IFS='-' read -r simulator computer date revision img <<< "$subdir"
        
        # Check if simulator and computer match user input
        if [[ "$simulator" == "$simulator_input" && "$computer" == "$computer_input" ]]; then
            # Get log info for this backup
            local log_info=""
            local log_path="/home/<USER>/Desktop/DriveDatabase/simulator_databases/$simulator_input/$computer_input.db"

            if [[ -f "$log_path" ]]; then
                # Debug: Check what's in the database
                echo "DEBUG: Checking log database at: $log_path" >&2
                echo "DEBUG: Looking for revision: $revision, computer: $computer_input" >&2

                # Try different column name variations that might exist
                log_info=$(sqlite3 "$log_path" "SELECT \"Log Comment\" FROM logs WHERE \"Revision Number\"='$revision' AND \"Action\"='Backup' LIMIT 1;" 2>/dev/null)

                # If that doesn't work, try without computer filter
                if [[ -z "$log_info" ]]; then
                    log_info=$(sqlite3 "$log_path" "SELECT \"Log Comment\" FROM logs WHERE \"Revision Number\"='$revision' LIMIT 1;" 2>/dev/null)
                fi

                # If still empty, try different column names
                if [[ -z "$log_info" ]]; then
                    log_info=$(sqlite3 "$log_path" "SELECT \"Log Comment\" FROM logs WHERE \"Revision\"='$revision' LIMIT 1;" 2>/dev/null)
                fi

                echo "DEBUG: Retrieved log info: '$log_info'" >&2
            fi

            # Store the directory for menu selection
            if [[ -n "$log_info" ]]; then
                subdirs+=("$subdir" "rev $revision - $log_info")
            else
                subdirs+=("$subdir" "rev $revision")
            fi

            # Track the highest revision
            if (( revision > highest_revision )); then
                highest_revision="$revision"
                highest_dir="$subdir"
            fi
        fi
    done

    # Check if a highest revision was found
    if [[ -n "$highest_dir" ]]; then
        # Get log info for highest revision
        IFS='-' read -r sim comp date rev img <<< "$highest_dir"
        local log_path="/home/<USER>/Desktop/DriveDatabase/simulator_databases/$simulator_input/$computer_input.db"
        local log_info=""

        if [[ -f "$log_path" ]]; then
            echo "DEBUG: Checking highest revision log database at: $log_path" >&2
            echo "DEBUG: Looking for highest revision: $rev, computer: $computer_input" >&2

            # Try different column name variations that might exist
            log_info=$(sqlite3 "$log_path" "SELECT \"Log Comment\" FROM logs WHERE \"Revision Number\"='$rev' AND \"Action\"='Backup' LIMIT 1;" 2>/dev/null)

            # If that doesn't work, try without computer filter
            if [[ -z "$log_info" ]]; then
                log_info=$(sqlite3 "$log_path" "SELECT \"Log Comment\" FROM logs WHERE \"Revision Number\"='$rev' LIMIT 1;" 2>/dev/null)
            fi

            # If still empty, try different column names
            if [[ -z "$log_info" ]]; then
                log_info=$(sqlite3 "$log_path" "SELECT \"Log Comment\" FROM logs WHERE \"Revision\"='$rev' LIMIT 1;" 2>/dev/null)
            fi

            echo "DEBUG: Retrieved highest revision log info: '$log_info'" >&2
        fi

        # Build menu title with proper newline handling
        local menu_title="Latest revision found: $highest_revision ($highest_dir)"
        if [[ -n "$log_info" ]]; then
            # Use $'\n' for actual newline in bash
            menu_title="$menu_title"$'\n'"Log: $log_info"
        fi

        use_latest=$(whiptail --title "Select a Backup Image" --menu "$menu_title" 0 0 0 \
        "Yes" "Use \"$highest_dir\"" \
        "No" "Select image from list instead" \
        3>&1 1>&2 2>&3)
        
        if [[ $use_latest = "Yes" ]]; then
            IFS='-' read -r simulator computer date rev img <<< "$highest_dir"
            echo "$highest_dir"
            echo "$rev"
            return 0
        fi
    fi

    # Check if there are any matching directories
    if [[ ${#subdirs[@]} -eq 0 ]]; then
        whiptail --msgbox "No matching Backups found for $simulator_input-$computer_input!" 35 105
        exit 1
    fi

    # Sort subdirs by date (most recent first)
    # Create a temporary array with sortable dates
    local temp_array=()
    local i=0
    while [ $i -lt ${#subdirs[@]} ]; do
        local backup_name="${subdirs[$i]}"
        local description="${subdirs[$((i+1))]}"

        # Extract date from backup name (format: simulator-computer-MM_DD_YYYY-revision-img)
        if [[ "$backup_name" =~ -([0-9]{2}_[0-9]{2}_[0-9]{4})- ]]; then
            local date_part="${BASH_REMATCH[1]}"
            # Convert MM_DD_YYYY to YYYY_MM_DD for sorting
            local month="${date_part:0:2}"
            local day="${date_part:3:2}"
            local year="${date_part:6:4}"
            local sortable_date="${year}_${month}_${day}"

            # Store with sortable date prefix
            temp_array+=("${sortable_date}|${backup_name}|${description}")
        else
            # If date format doesn't match, put at end
            temp_array+=("0000_00_00|${backup_name}|${description}")
        fi

        i=$((i+2))
    done

    # Sort the array (most recent first - reverse sort)
    IFS=$'\n' sorted_temp=($(sort -t'|' -k1,1r <<< "${temp_array[*]}"))

    # Rebuild subdirs array from sorted data
    subdirs=()
    for entry in "${sorted_temp[@]}"; do
        IFS='|' read -r sortable_date backup_name description <<< "$entry"
        subdirs+=("$backup_name" "$description")
    done

    # Show menu of matching directories (now sorted by date, most recent first)
    selected=$(whiptail --title "Select a Backup Image" --menu "Choose a backup image:" 20 80 10 "${subdirs[@]}" 3>&1 1>&2 2>&3)

    # Exit if the user cancels
    if [[ $? -ne 0 ]]; then
        echo "No selection made."
        exit 1
    fi

    # Output the selected directory
    IFS='-' read -r simulator computer date rev img <<< "$selected"
    echo "$selected"
    echo "$rev"
}

#
# get_backup_size_for_computer() - Calculate optimal backup size based on previous backups
#
# Parameters:
#   $1 - Simulator name
#   $2 - Computer name
#
# Returns:
#   Echoes backup size in bytes, or "0" if no previous backups found
#
# Description:
#   Analyzes previous backup files to determine optimal backup size for smart SD card copying.
#   This helps avoid copying unnecessary empty space on SD cards.
#
get_backup_size_for_computer() {
    local simulator="$1"
    local computer="$2"

    # Send debug output to stderr so it doesn't interfere with return value
    echo "Calculating optimal backup size for $computer..." >&2
    echo "   Finding most recent backup" >&2

    # Use test directory if set, otherwise use standard location
    local partimag_dir="${TEST_PARTIMAG_DIR:-/home/<USER>"

    # Find most recent backup
    local latest_backup=""
    local latest_date=""

    for backup_file in ${partimag_dir}/${simulator}-${computer}-*-img; do
        if [ -f "$backup_file" ]; then
            local filename=$(basename "$backup_file")
            if [[ "$filename" =~ -([0-9]{8})- ]]; then
                local backup_date="${BASH_REMATCH[1]}"
                if [[ "$backup_date" > "$latest_date" ]]; then
                    latest_date="$backup_date"
                    latest_backup="$backup_file"
                fi
            fi
        fi
    done

    if [ -n "$latest_backup" ]; then
        local size=$(stat -c%s "$latest_backup" 2>/dev/null || echo "0")
        local size_mb=$((size / 1048576))
        echo "   Using latest backup size: ${size_mb} MB" >&2
        echo "$size"
    else
        echo "   No previous backups found" >&2
        echo "0"
    fi
}



# Function to verify SCSI2SD config exists in backup image
verify_scsi2sd_config_in_backup() {
    local backup_image="$1"

    echo "Verifying SCSI2SD config in backup image: $backup_image" >&2

    if [ ! -f "$backup_image" ]; then
        echo "Error: Backup image file not found: $backup_image" >&2
        return 1
    fi

    # Get backup image size
    local backup_size=$(stat -c%s "$backup_image" 2>/dev/null || echo "0")
    if [ "$backup_size" = "0" ]; then
        echo "Error: Cannot determine backup image size" >&2
        return 1
    fi

    # Check if backup is large enough to contain config (at least 1024 bytes)
    if [ "$backup_size" -lt 1024 ]; then
        echo "Warning: Backup image too small to contain SCSI2SD config" >&2
        return 1
    fi

    # Extract last 1024 bytes (SCSI2SD config)
    local config_offset=$((backup_size - 1024))
    local temp_config="/tmp/verify_scsi2sd_config.bin"
    dd if="$backup_image" of="$temp_config" bs=1024 count=1 skip=$((config_offset / 1024)) 2>/dev/null

    # Check for SCSI2SD magic signature "BCFG" (42434647 in hex)
    local magic=$(dd if="$temp_config" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')

    if [ "$magic" = "42434647" ]; then
        echo "✓ SCSI2SD config verified in backup image" >&2
        rm -f "$temp_config"
        return 0
    else
        echo "✗ No SCSI2SD config found in backup image (magic: $magic)" >&2
        rm -f "$temp_config"
        return 1
    fi
}

# Function to detect SD card in identification slot
detect_sd_card_for_identification() {
    echo "Detecting SD card in identification slot..."

    for file in /dev/sd*; do
        if [ ! -b "$file" ]; then
            continue
        fi

        local dev="$file"
        # Check udev info for SD card reader
        local udev_info=$(udevadm info --query=all --name="$dev" 2>/dev/null)

        # Look for SD card reader that's NOT the UHS-II slot
        if echo "$udev_info" | grep -q 'ID_VENDOR.*SD_Card_Reader' && \
           ! echo "$udev_info" | grep -q 'ID_MODEL.*SD_UHS_II'; then

            # Get serial number for identification
            local serial=$(sudo hdparm -I "$dev" 2>/dev/null | grep "Serial Number" | awk '{print $3}')
            if [ -n "$serial" ]; then
                echo "Found SD card in identification slot: $dev"
                echo "   Serial: $serial"
                echo "$dev|$serial"
                return 0
            fi
        fi
    done

    echo "No SD card found in identification slot"
    return 1
}

# Function to detect UHS-II slot device
detect_uhs2_slot() {
    for file in /dev/sd*; do
        if [ ! -b "$file" ]; then
            continue
        fi

        local dev="$file"
        # Check for UHS-II specific identifiers from your udev rules
        local udev_info=$(udevadm info --query=all --name="$dev" 2>/dev/null)

        if echo "$udev_info" | grep -q 'ID_MODEL.*SD_UHS_II' && \
           echo "$udev_info" | grep -q 'ID_INSTANCE.*0:1'; then
            echo "$dev"
            return 0
        fi
    done

    return 1
}

# Function to wait for SD card in UHS-II slot
wait_for_sd_card_in_uhs2_slot() {
    local expected_serial="$1"

    echo "Waiting for SD card to appear in UHS-II slot..."
    echo "   Expected serial: $expected_serial"

    local timeout=60  # 60 second timeout
    local count=0

    while [ $count -lt $timeout ]; do
        # Check for SD card in UHS-II slot
        for file in /dev/sd*; do
            if [ ! -b "$file" ]; then
                continue
            fi

            local dev="$file"
            local udev_info=$(udevadm info --query=all --name="$dev" 2>/dev/null)

            # Check if this is the UHS-II slot
            if echo "$udev_info" | grep -q 'ID_MODEL.*SD_UHS_II' && \
               echo "$udev_info" | grep -q 'ID_INSTANCE.*0:1'; then

                # Check if it has the expected serial
                local serial=$(sudo hdparm -I "$dev" 2>/dev/null | grep "Serial Number" | awk '{print $3}')
                if [[ "$serial" == "$expected_serial" ]]; then
                    echo "SD card detected in UHS-II slot: $dev"
                    echo "   Serial confirmed: $serial"
                    echo "$dev"
                    return 0
                fi
            fi
        done

        sleep 1
        count=$((count + 1))

        # Show progress every 10 seconds
        if [ $((count % 10)) -eq 0 ]; then
            echo "   Still waiting... (${count}s elapsed)"
        fi
    done

    echo "Timeout waiting for SD card in UHS-II slot"
    return 1
}

# Function to detect and parse SCSI2SD configuration from device
# Returns the exact size needed for backup based on configured drives
detect_scsi2sd_config() {
    local device="$1"

    echo "Detecting SCSI2SD configuration on $device..." >&2

    if [ ! -b "$device" ]; then
        echo "Device $device is not a block device" >&2
        echo "0"
        return 1
    fi

    # Get device size and calculate config location
    # Try with sudo if regular access fails (for test scripts)
    local device_size=$(blockdev --getsize64 "$device" 2>/dev/null || sudo blockdev --getsize64 "$device" 2>/dev/null || echo "0")
    if [ "$device_size" = "0" ]; then
        echo "Could not determine device size" >&2
        echo "0"
        return 1
    fi

    local device_sectors=$((device_size / 512))
    local config_start_sector=$((device_sectors - 2))

    echo "   Reading config from sectors $config_start_sector-$((device_sectors - 1))..." >&2

    # Read the 2-sector config (try with sudo if needed)
    local config_data=$(dd if="$device" bs=512 count=2 skip=$config_start_sector 2>/dev/null | od -tx1 -An | tr -d ' \n')
    if [ -z "$config_data" ]; then
        config_data=$(sudo dd if="$device" bs=512 count=2 skip=$config_start_sector 2>/dev/null | od -tx1 -An | tr -d ' \n')
    fi

    if [ -z "$config_data" ]; then
        echo "   Failed to read config data" >&2
        echo "0"
        return 1
    fi

    # Check for SCSI2SD magic signature "BCFG" (42434647 in hex)
    local magic="${config_data:0:8}"
    if [ "$magic" != "42434647" ]; then
        echo "   No valid SCSI2SD config found (magic: $magic)" >&2
        echo "0"
        return 1
    fi

    echo "   Valid SCSI2SD configuration detected" >&2

    # Parse target configurations using binary data instead of hex string
    # Save config to temp file for binary parsing
    local temp_config="/tmp/scsi2sd_parse_$$.bin"
    dd if="$device" bs=512 count=2 skip=$config_start_sector of="$temp_config" 2>/dev/null || \
    sudo dd if="$device" bs=512 count=2 skip=$config_start_sector of="$temp_config" 2>/dev/null

    local max_end_sector=0
    local active_drives=0

    # Each target config is 128 bytes, starting at offset 128 in the config
    for target_id in {0..6}; do
        local byte_offset=$((128 + target_id * 128))

        # Check if we have enough data
        if [ $byte_offset -ge 1024 ]; then
            break
        fi

        # Read SCSI ID byte (first byte of target config)
        local scsi_id_byte=$(dd if="$temp_config" bs=1 count=1 skip=$byte_offset 2>/dev/null | od -tu1 -An | tr -d ' ')

        if [ -n "$scsi_id_byte" ] && [ "$scsi_id_byte" != "0" ]; then
            local scsi_id=$((scsi_id_byte & 0x07))
            local enabled=$((scsi_id_byte & 0x80))

            # Check if target has meaningful configuration (non-zero SCSI sectors)
            local sectors_offset=$((byte_offset + 8))
            local scsi_sectors_bytes=$(dd if="$temp_config" bs=4 count=1 skip=$((sectors_offset / 4)) 2>/dev/null | od -tu1 -An)
            local scsi_sectors=0

            # Parse little-endian 32-bit value
            if [ -n "$scsi_sectors_bytes" ]; then
                local bytes=($scsi_sectors_bytes)
                if [ ${#bytes[@]} -eq 4 ]; then
                    scsi_sectors=$((${bytes[0]} + ${bytes[1]} * 256 + ${bytes[2]} * 65536 + ${bytes[3]} * 16777216))
                fi
            fi

            if [ $scsi_sectors -gt 0 ]; then
                active_drives=$((active_drives + 1))

                # Get SD start sector (bytes 4-7, little endian)
                local start_offset=$((byte_offset + 4))
                local start_bytes=$(dd if="$temp_config" bs=4 count=1 skip=$((start_offset / 4)) 2>/dev/null | od -tu1 -An)
                local sd_start_sector=0

                if [ -n "$start_bytes" ]; then
                    local bytes=($start_bytes)
                    if [ ${#bytes[@]} -eq 4 ]; then
                        sd_start_sector=$((${bytes[0]} + ${bytes[1]} * 256 + ${bytes[2]} * 65536 + ${bytes[3]} * 16777216))
                    fi
                fi

                # Calculate end sector for this drive
                local end_sector=$((sd_start_sector + scsi_sectors))

                local drive_mb=$((scsi_sectors * 512 / 1048576))
                echo "   Drive $target_id (SCSI ID $scsi_id): ${drive_mb} MB, sectors $sd_start_sector-$end_sector" >&2

                if [ $end_sector -gt $max_end_sector ]; then
                    max_end_sector=$end_sector
                fi
            fi
        fi
    done

    # Clean up temp file
    rm -f "$temp_config"

    if [ $active_drives -eq 0 ]; then
        echo "   No active drives found in config" >&2
        echo "0"
        return 1
    fi

    # Calculate exact backup size: data + config
    local data_size_bytes=$((max_end_sector * 512))
    local config_size_bytes=1024  # 2 sectors
    local total_backup_size=$((data_size_bytes + config_size_bytes))
    local total_mb=$((total_backup_size / 1048576))

    echo "   Active drives: $active_drives" >&2
    echo "   Data ends at sector: $max_end_sector" >&2
    echo "   Exact backup size needed: ${total_mb} MB" >&2

    echo "$total_backup_size"
    return 0
}

# Function to restore with SCSI2SD config handling
restore_with_scsi2sd_config() {
    local backup_image="$1"
    local target_device="$2"

    echo "Restoring with SCSI2SD config handling..." >&2

    # Get backup image size
    local backup_size=$(stat -c%s "$backup_image" 2>/dev/null || echo "0")
    if [ "$backup_size" = "0" ]; then
        echo "Error: Cannot determine backup image size" >&2
        return 1
    fi

    # Extract config from last 1024 bytes of backup image
    local config_offset=$((backup_size - 1024))
    local temp_config="/tmp/backup_scsi2sd_config.bin"
    dd if="$backup_image" of="$temp_config" bs=1024 count=1 skip=$((config_offset / 1024)) 2>/dev/null

    # Check if backup contains valid SCSI2SD config
    local backup_config_magic=$(dd if="$temp_config" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')

    if [ "$backup_config_magic" = "42434647" ]; then  # "BCFG" in hex
        echo "   Valid SCSI2SD config found in backup image" >&2

        # Check if target device already has SCSI2SD config
        local target_device_size=$(blockdev --getsize64 "$target_device" 2>/dev/null || echo "0")
        if [ "$target_device_size" != "0" ]; then
            local target_sectors=$((target_device_size / 512))
            local target_config_sector=$((target_sectors - 2))

            # Read existing config from target
            local existing_config_magic=$(dd if="$target_device" bs=4 count=1 skip=$((target_config_sector * 512 / 4)) 2>/dev/null | od -tx1 -An | tr -d ' ')

            if [ "$existing_config_magic" = "42434647" ]; then
                echo "   Target device already has SCSI2SD config - preserving it" >&2
                # Restore only the data portion (without config)
                local data_size=$((backup_size - 1024))
                dd if="$backup_image" of="$target_device" bs=1M count=$((data_size / 1048576)) 2>/dev/null
            else
                echo "   Target device has no SCSI2SD config - writing config from backup" >&2
                # Restore data portion
                local data_size=$((backup_size - 1024))
                dd if="$backup_image" of="$target_device" bs=1M count=$((data_size / 1048576)) 2>/dev/null

                # Write config to end of target device
                dd if="$temp_config" of="$target_device" bs=512 count=2 seek=$target_config_sector 2>/dev/null
            fi
        else
            echo "   Error: Cannot determine target device size" >&2
            rm -f "$temp_config"
            return 1
        fi
    else
        echo "   No SCSI2SD config in backup - performing regular restore" >&2
        # Regular restore without config handling
        dd if="$backup_image" of="$target_device" bs=1M 2>/dev/null
    fi

    rm -f "$temp_config"
    return 0
}

# Function to extract serial number from SCSI2SD config
# Returns the "revision" field which contains the revision number used as the serial number
get_scsi2sd_serial_number() {
    local device="$1"

    echo "Extracting serial number from SCSI2SD config on $device..." >&2

    if [ ! -b "$device" ]; then
        echo "Device $device is not a block device" >&2
        echo ""
        return 1
    fi

    # Get device size and calculate config location
    local device_size=$(blockdev --getsize64 "$device" 2>/dev/null || sudo blockdev --getsize64 "$device" 2>/dev/null || echo "0")
    if [ "$device_size" = "0" ]; then
        echo "Could not determine device size" >&2
        echo ""
        return 1
    fi

    local device_sectors=$((device_size / 512))
    local config_start_sector=$((device_sectors - 2))

    # Read the 2-sector config
    local temp_config="/tmp/scsi2sd_serial_$$.bin"
    dd if="$device" bs=512 count=2 skip=$config_start_sector of="$temp_config" 2>/dev/null || \
    sudo dd if="$device" bs=512 count=2 skip=$config_start_sector of="$temp_config" 2>/dev/null

    if [ ! -f "$temp_config" ]; then
        echo "Failed to read config data" >&2
        echo ""
        return 1
    fi

    # Check for SCSI2SD magic signature "BCFG"
    local magic=$(dd if="$temp_config" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')
    if [ "$magic" != "42434647" ]; then
        echo "No valid SCSI2SD config found" >&2
        rm -f "$temp_config"
        echo ""
        return 1
    fi

    echo "Valid SCSI2SD configuration detected" >&2

    # Find the first active drive and extract its revision field (used as serial number)
    for target_id in {0..6}; do
        local byte_offset=$((128 + target_id * 128))

        if [ $byte_offset -ge 1024 ]; then
            break
        fi

        # Read SCSI ID byte (first byte of target config)
        local scsi_id_byte=$(dd if="$temp_config" bs=1 count=1 skip=$byte_offset 2>/dev/null | od -tu1 -An | tr -d ' ')

        if [ -n "$scsi_id_byte" ] && [ "$scsi_id_byte" != "0" ]; then
            # Check if target has meaningful configuration (non-zero SCSI sectors)
            local sectors_offset=$((byte_offset + 8))
            local scsi_sectors_bytes=$(dd if="$temp_config" bs=4 count=1 skip=$((sectors_offset / 4)) 2>/dev/null | od -tu1 -An)
            local scsi_sectors=0

            if [ -n "$scsi_sectors_bytes" ]; then
                local bytes=($scsi_sectors_bytes)
                if [ ${#bytes[@]} -eq 4 ]; then
                    scsi_sectors=$((${bytes[0]} + ${bytes[1]} * 256 + ${bytes[2]} * 65536 + ${bytes[3]} * 16777216))
                fi
            fi

            if [ $scsi_sectors -gt 0 ]; then
                # Extract revision field (bytes 42-45, 4 bytes) - this contains the revision number we want as serial
                local revision_offset=$((byte_offset + 42))
                local revision_raw=$(dd if="$temp_config" bs=1 count=4 skip=$revision_offset 2>/dev/null | tr -d '\0' | strings)

                if [ -n "$revision_raw" ]; then
                    # Clean up the revision string and extract just the meaningful part
                    local revision_clean=$(echo "$revision_raw" | sed 's/[[:space:]]*$//' | sed 's/^[[:space:]]*//')

                    # Remove any trailing non-alphanumeric characters
                    revision_clean=$(echo "$revision_clean" | sed 's/[^a-zA-Z0-9]*$//')

                    if [ -n "$revision_clean" ]; then
                        echo "Found serial number from SCSI2SD config revision field: '$revision_clean'" >&2
                        rm -f "$temp_config"
                        echo "$revision_clean"
                        return 0
                    fi
                fi
            fi
        fi
    done

    echo "No active drives found in SCSI2SD config" >&2
    rm -f "$temp_config"
    echo ""
    return 1
}

# Function to perform SD card operations with UHS-II optimization and smart sizing
SD_drive_action() {
    local optype="$1"
    local target_dev="$2"
    local backupname="$3"
    local rev_num="$4"
    local at="$5"
    local serial_number="$6"
    local simulator="$7"
    local computer="$8"
    local enable_uhs2="${9:-false}"

    # UHS-II optimization is controlled by whether "SD" was selected in sd_card_or_hd()
    # This works regardless of how the device appears to the system (SCSI2SD, etc.)

    # Step 1: UHS-II slot optimization (only when SD was selected)
    if [ "$enable_uhs2" = "true" ]; then
        echo "Starting SD card operation with UHS-II optimization..."
        echo "   (SD was selected in menu - UHS-II optimization enabled)"

        local operation_type="backup"
        if [ "$optype" = "restoredisk" ]; then
            operation_type="restore"
        fi

        echo "Checking for UHS-II optimization opportunity..."

        # Check if UHS-II slot is available
        local uhs2_device=$(detect_uhs2_slot)
        if [ $? -eq 0 ] && [ -n "$uhs2_device" ]; then
            echo "UHS-II slot detected: $uhs2_device"
            echo "Using UHS-II optimized device: $uhs2_device"
            target_dev="$uhs2_device"
        else
            echo "UHS-II slot not found, using original device: $target_dev"
        fi
    else
        echo "Starting operation without UHS-II optimization..."
        echo "   (Regular HDD was selected or UHS-II not enabled)"
    fi

    # Verify target_dev is a block device or file (for testing)
    if [ ! -b "$target_dev" ] && [ ! -f "$target_dev" ]; then
        echo "Error: $target_dev is not a valid block device or file"
        return 1
    fi

    # Step 2: Smart sizing for backups (only when SD was selected)
    local size_limit=""
    if [ "$enable_uhs2" = "true" ] && [ "$optype" = "savedisk" ] && [ -n "$simulator" ] && [ -n "$computer" ]; then
        echo "Calculating optimal backup size..."

        # First try SCSI2SD config detection for exact sizing
        local scsi2sd_size=$(detect_scsi2sd_config "$target_dev")
        if [ "$scsi2sd_size" != "0" ]; then
            local scsi2sd_mb=$((scsi2sd_size / 1048576))
            echo "Using SCSI2SD config sizing: ${scsi2sd_mb} MB (exact drive configuration)"
            size_limit="$scsi2sd_size"
        else
            # Fallback to previous backup analysis
            local optimal_size=$(get_backup_size_for_computer "$simulator" "$computer")
            if [ "$optimal_size" != "0" ]; then
                local optimal_mb=$((optimal_size / 1048576))
                echo "Using smart sizing: ${optimal_mb} MB (based on previous backups)"
                size_limit="$optimal_size"
            else
                echo "No previous backups found and no SCSI2SD config - copying full device"
            fi
        fi
    fi

    # Step 3: Proceed with optimized backup/restore
    echo "Using pv+mbuffer for fast disk imaging"
    local operation_result=0
    if [ -n "$size_limit" ]; then
        SD_drive_action_with_smart_size "$optype" "$target_dev" "$backupname" "$rev_num" "$at" "$serial_number" "$size_limit"
        operation_result=$?
    else
        SD_drive_action_with_ddrescue "$optype" "$target_dev" "$backupname" "$rev_num" "$at" "$serial_number"
        operation_result=$?
    fi

    # Step 4: Update database and check siblings after successful backup
    if [ "$operation_result" = "0" ] && [ "$optype" = "savedisk" ]; then
        echo "Updating revision number in database..."
        local cmd="UPDATE Drives SET \"Revision Number\" = '$rev_num' WHERE \"Asset Tag\" = '$at' AND \"Serial Number\" = '$serial_number';"
        sqlite3 "$database_path" "$cmd"

        if [ $? = "0" ]; then
            echo "Revision number set for \"$at\" rev num \"$rev_num\""

            # Check and update sibling drives after successful backup
            check_and_update_sibling_drives "$serial_number" "$rev_num" "$database_path"
        else
            echo "Error: Failed to update revision number in database"
        fi
    fi

    return $operation_result
}

# Function using ddrescue for fastest disk imaging with progress
SD_drive_action_with_ddrescue() {
    local optype="$1"
    local target_dev="$2"
    local backupname="$3"
    local rev_num="$4"
    local at="$5"
    local serial_number="$6"

    # Use test directory if set, otherwise use standard location
    local partimag_dir="${TEST_PARTIMAG_DIR:-/home/<USER>"

    if [ "$optype" = "savedisk" ]; then
        local operation_title="Creating Backup Image (pv+mbuffer)"
        local operation_desc="Backing up $target_dev to $backupname"
        local source="$target_dev"
        local destination="$partimag_dir/$backupname"
    else
        if [ ! -f "$partimag_dir/$backupname" ]; then
            echo "Error: Backup file $partimag_dir/$backupname not found"
            return 1
        fi
        local operation_title="Restoring Backup Image (pv+mbuffer)"
        local operation_desc="Restoring $backupname to $target_dev"
        local source="$partimag_dir/$backupname"
        local destination="$target_dev"
    fi

    # Check if mbuffer is available
    if ! command -v mbuffer >/dev/null 2>&1; then
        echo "ERROR: mbuffer not found. Please install mbuffer first."
        echo "   Run: sudo apt-get install mbuffer"
        return 1
    fi

    # Get source size for pv progress monitoring
    local source_size=0
    if [ "$optype" = "savedisk" ]; then
        if [ -b "$source" ]; then
            source_size=$(blockdev --getsize64 "$source" 2>/dev/null || echo "0")
        else
            source_size=$(stat -c%s "$source" 2>/dev/null || echo "0")
        fi
    else
        source_size=$(stat -c%s "$source" 2>/dev/null || echo "0")
    fi

    # Create temporary file for pv output
    local pv_output=$(mktemp /tmp/pv_progress.XXXXXX)
    trap "rm -f '$pv_output'" EXIT

    # Start pv + mbuffer pipeline in background
    {
        # Pipeline: source -> pv (for progress) -> mbuffer (for performance) -> destination
        # This combines pv's excellent progress reporting with mbuffer's superior buffering
        pv -s "$source_size" -n -i 0.5 "$source" 2>"$pv_output" | \
        mbuffer -s 64k -m 512M -q -o "$destination" >/dev/null 2>&1
        echo $? > /tmp/pv_mbuffer_exit_code_$$
    } &
    local copy_pid=$!

    # Monitor progress in foreground with dialog
    local start_time=$(date +%s)
    local last_percent=0
    local last_time=$start_time
    local last_bytes=0

    while kill -0 $copy_pid 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        # Read progress from pv output (reliable and accurate)
        local percent=0
        local speed_text="Calculating..."
        local current_bytes=0

        if [ -f "$pv_output" ]; then
            # Get the latest percentage from pv
            local pv_line=$(tail -n1 "$pv_output" 2>/dev/null | tr -d '\n\r')
            percent=$(echo "$pv_line" | grep -o '[0-9]*' | tail -n1)
            if [[ ! "$percent" =~ ^[0-9]+$ ]]; then
                percent=0
            fi
            if [ $percent -gt 100 ]; then
                percent=100
            fi
            # Calculate current bytes from percentage
            current_bytes=$((source_size * percent / 100))
        fi

        # Calculate current speed from progress data
        local time_diff=$((current_time - last_time))
        local bytes_diff=$((current_bytes - last_bytes))

        if [ $time_diff -gt 0 ] && [ $bytes_diff -gt 0 ]; then
            local speed_bps=$((bytes_diff / time_diff))
            if [ $speed_bps -gt 1048576 ]; then
                speed_text="$(echo "scale=1; $speed_bps / 1048576" | bc -l 2>/dev/null) MB/s"
            elif [ $speed_bps -gt 1024 ]; then
                speed_text="$(echo "scale=1; $speed_bps / 1024" | bc -l 2>/dev/null) KB/s"
            else
                speed_text="${speed_bps} B/s"
            fi
        fi

        # Calculate average speed
        # Calculate average speed
        local avg_speed_text="N/A"
        if [ $elapsed -gt 0 ] && [ $current_bytes -gt 0 ]; then
            local avg_speed_bps=$((current_bytes / elapsed))
            if [ $avg_speed_bps -gt 1048576 ]; then
                avg_speed_text="$(echo "scale=1; $avg_speed_bps / 1048576" | bc -l 2>/dev/null) MB/s"
            elif [ $avg_speed_bps -gt 1024 ]; then
                avg_speed_text="$(echo "scale=1; $avg_speed_bps / 1024" | bc -l 2>/dev/null) KB/s"
            else
                avg_speed_text="${avg_speed_bps} B/s"
            fi
        fi

        # Create progress bar
        local bar=""
        local filled=$((percent / 2))
        for ((i=0; i<filled; i++)); do bar+="█"; done
        for ((i=filled; i<50; i++)); do bar+="░"; done

        # Format sizes for display
        local current_mb=$((current_bytes / 1048576))
        local source_mb=$((source_size / 1048576))

        # Display progress with dialog
        if command -v dialog >/dev/null 2>&1; then
            dialog --title "$operation_title" --infobox "
$operation_desc

Progress: $percent%
[$bar]

Current Speed: $speed_text
Average Speed: $avg_speed_text
Copied: ${current_mb} MB / ${source_mb} MB
Time: ${elapsed}s

PV+MBuffer: Enhanced buffering with real-time progress
Press Ctrl+C to cancel" 15 70
        else
            # Fallback to simple text output if dialog not available
            clear
            echo "=== $operation_title ==="
            echo "$operation_desc"
            echo ""
            echo "Progress: $percent%"
            echo "[$bar]"
            echo ""
            echo "Current Speed: $speed_text"
            echo "Average Speed: $avg_speed_text"
            echo "Copied: ${current_mb} MB / ${source_mb} MB"
            echo "Time: ${elapsed}s"
            echo ""
            echo "PV+MBuffer: Enhanced buffering with real-time progress"
            echo "Press Ctrl+C to cancel"
        fi

        # Update for next iteration
        last_percent=$percent
        last_time=$current_time
        last_bytes=$current_bytes

        sleep 1
    done

    # Wait for completion
    wait $copy_pid
    local exit_code=$(cat /tmp/pv_mbuffer_exit_code_$$ 2>/dev/null || echo "1")
    rm -f /tmp/pv_mbuffer_exit_code_$$

    # Clean up pv output file
    rm -f "$pv_output"

    # Show final completion dialog
    local total_time=$(($(date +%s) - start_time))
    local final_size=$(stat -c%s "$destination" 2>/dev/null || echo "0")
    local final_mb=$((final_size / 1048576))

    if command -v dialog >/dev/null 2>&1; then
        dialog --title "$operation_title" --msgbox "Operation completed!

Data: ${final_mb} MB
Time: ${total_time}s

Press OK to continue" 10 40
    else
        whiptail --title "$operation_title" --msgbox "Operation completed!

Data: ${final_mb} MB
Time: ${total_time}s

Press OK to continue" 10 40 3>&1 1>&2 2>&3
    fi

    return $exit_code
}

# Function to perform SD card operations with smart size limiting
SD_drive_action_with_smart_size() {
    local optype="$1"
    local target_dev="$2"
    local backupname="$3"
    local rev_num="$4"
    local at="$5"
    local serial_number="$6"
    local size_limit="$7"

    # Set up partimag directory
    local partimag_dir="${TEST_PARTIMAG_DIR:-/home/<USER>"

    if [ "$optype" = "savedisk" ]; then
        local operation_title="Creating Smart SD Backup (pv+mbuffer)"
        local operation_desc="Smart backup of $target_dev to $backupname"
        local source="$target_dev"
        local destination="$partimag_dir/$backupname"
        local source_size="$size_limit"
    else
        if [ ! -f "$partimag_dir/$backupname" ]; then
            echo "Error: Backup file $partimag_dir/$backupname not found"
            return 1
        fi
        local operation_title="Restoring SD Image (pv+mbuffer)"
        local operation_desc="Restoring $backupname to $target_dev"
        local source="$partimag_dir/$backupname"
        local destination="$target_dev"
        local source_size=$(stat -c%s "$source" 2>/dev/null || echo "0")
    fi

    # Check if mbuffer is available
    if ! command -v mbuffer >/dev/null 2>&1; then
        echo "ERROR: mbuffer not found. Please install mbuffer first."
        echo "   Run: sudo apt-get install mbuffer"
        return 1
    fi

    echo "Smart SD card operation:"
    echo "   Operation: $operation_title"
    echo "   Source: $source"
    echo "   Destination: $destination"
    local size_limit_mb=$((source_size / 1048576))
    echo "   Size limit: ${size_limit_mb} MB"

    # Create temporary file for pv output
    local pv_output=$(mktemp /tmp/pv_progress.XXXXXX)
    trap "rm -f '$pv_output'" EXIT

    # Start pv + mbuffer pipeline with size limiting
    local start_time=$(date +%s)
    {
        if [ "$optype" = "savedisk" ]; then
            # Smart backup: copy data + append SCSI2SD config
            local data_size=$((size_limit - 1024))  # Subtract config size
            dd if="$source" bs=1M count=$((data_size / 1048576)) iflag=fullblock 2>/dev/null | \
            pv -s "$source_size" -n -i 0.5 2>"$pv_output" | \
            mbuffer -s 64k -m 512M -q -o "$destination" >/dev/null 2>&1

            # Append SCSI2SD config to the backup image
            local device_size=$(blockdev --getsize64 "$source" 2>/dev/null || echo "0")
            if [ "$device_size" != "0" ]; then
                local device_sectors=$((device_size / 512))
                local config_start_sector=$((device_sectors - 2))
                echo "   Appending SCSI2SD config to backup..." >&2
                dd if="$source" bs=512 count=2 skip=$config_start_sector >> "$destination" 2>/dev/null
            fi
        else
            # Restore: handle SCSI2SD config restoration
            restore_with_scsi2sd_config "$source" "$destination"
        fi
        echo $? > /tmp/smart_sd_exit_code_$$
    } &
    local copy_pid=$!

    # Monitor progress with dialog (same as before)
    local last_percent=0
    local last_time=$start_time
    local last_bytes=0

    while kill -0 $copy_pid 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        # Read progress from pv output
        local percent=0
        local speed_text="Calculating..."
        local current_bytes=0

        if [ -f "$pv_output" ]; then
            local pv_line=$(tail -n1 "$pv_output" 2>/dev/null | tr -d '\n\r')
            percent=$(echo "$pv_line" | grep -o '[0-9]*' | tail -n1)
            if [[ ! "$percent" =~ ^[0-9]+$ ]]; then
                percent=0
            fi
            if [ $percent -gt 100 ]; then
                percent=100
            fi
            current_bytes=$((source_size * percent / 100))
        fi

        # Calculate current speed
        local time_diff=$((current_time - last_time))
        local bytes_diff=$((current_bytes - last_bytes))

        if [ $time_diff -gt 0 ] && [ $bytes_diff -gt 0 ]; then
            local speed_bps=$((bytes_diff / time_diff))
            if [ $speed_bps -gt 1048576 ]; then
                speed_text="$(echo "scale=1; $speed_bps / 1048576" | bc -l 2>/dev/null) MB/s"
            elif [ $speed_bps -gt 1024 ]; then
                speed_text="$(echo "scale=1; $speed_bps / 1024" | bc -l 2>/dev/null) KB/s"
            else
                speed_text="${speed_bps} B/s"
            fi
        fi

        # Calculate average speed
        local avg_speed_text="N/A"
        if [ $elapsed -gt 0 ] && [ $current_bytes -gt 0 ]; then
            local avg_speed_bps=$((current_bytes / elapsed))
            if [ $avg_speed_bps -gt 1048576 ]; then
                avg_speed_text="$(echo "scale=1; $avg_speed_bps / 1048576" | bc -l 2>/dev/null) MB/s"
            elif [ $avg_speed_bps -gt 1024 ]; then
                avg_speed_text="$(echo "scale=1; $avg_speed_bps / 1024" | bc -l 2>/dev/null) KB/s"
            else
                avg_speed_text="${avg_speed_bps} B/s"
            fi
        fi

        # Create progress bar
        local bar=""
        local filled=$((percent / 2))
        for ((i=0; i<filled; i++)); do bar+="█"; done
        for ((i=filled; i<50; i++)); do bar+="░"; done

        # Format sizes for display
        local current_mb=$((current_bytes / 1048576))
        local source_mb=$((source_size / 1048576))

        # Display progress with dialog
        if command -v dialog >/dev/null 2>&1; then
            dialog --title "$operation_title" --infobox "
$operation_desc

Progress: $percent%
[$bar]

Current Speed: $speed_text
Average Speed: $avg_speed_text
Copied: ${current_mb} MB / ${source_mb} MB
Time: ${elapsed}s

Smart Sizing: Optimized for previous backup sizes
Press Ctrl+C to cancel" 16 70
        else
            # Fallback to simple text output
            clear
            echo "=== $operation_title ==="
            echo "$operation_desc"
            echo ""
            echo "Progress: $percent%"
            echo "[$bar]"
            echo ""
            echo "Current Speed: $speed_text"
            echo "Average Speed: $avg_speed_text"
            echo "Copied: ${current_mb} MB / ${source_mb} MB"
            echo "Time: ${elapsed}s"
            echo ""
            echo "Smart Sizing: Optimized for previous backup sizes"
            echo "Press Ctrl+C to cancel"
        fi

        # Update for next iteration
        last_percent=$percent
        last_time=$current_time
        last_bytes=$current_bytes

        sleep 1
    done

    # Wait for completion
    wait $copy_pid
    local exit_code=$(cat /tmp/smart_sd_exit_code_$$ 2>/dev/null || echo "1")
    rm -f /tmp/smart_sd_exit_code_$$

    # Clean up pv output file
    rm -f "$pv_output"

    # Verify SCSI2SD config in backup image (for SD card backups only)
    if [ "$optype" = "savedisk" ] && [ "$exit_code" = "0" ]; then
        echo "Performing post-backup verification..."
        verify_scsi2sd_config_in_backup "$destination"
        local verify_result=$?
        if [ $verify_result -ne 0 ]; then
            echo "Warning: SCSI2SD config verification failed"
            # Don't fail the entire operation, just warn
        fi
    fi

    # Show final completion dialog
    local total_time=$(($(date +%s) - start_time))
    local final_size=$(stat -c%s "$destination" 2>/dev/null || echo "0")
    local final_mb=$((final_size / 1048576))

    if command -v dialog >/dev/null 2>&1; then
        dialog --title "$operation_title" --msgbox "Smart SD operation completed!

Data: ${final_mb} MB
Time: ${total_time}s
Smart sizing: Optimized transfer

Press OK to continue" 12 50
    else
        whiptail --title "$operation_title" --msgbox "Smart SD operation completed!

Data: ${final_mb} MB
Time: ${total_time}s
Smart sizing: Optimized transfer

Press OK to continue" 12 50 3>&1 1>&2 2>&3
    fi

    return $exit_code
}

# Removed pv and basic dd functions - using ddrescue only
