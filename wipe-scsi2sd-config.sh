#!/bin/bash

# Quick script to wipe SCSI2SD configuration from SD card

echo "=== SCSI2SD Configuration Wiper ==="
echo

# Source the functions for UHS-II detection
source usr/sbin/ocs-flyright-functions

# Find the UHS-II slot device
echo "=== Finding UHS-II Slot Device ==="
device=$(detect_uhs2_slot)

if [ $? -eq 0 ] && [ -n "$device" ]; then
    echo "✓ Found UHS-II slot device: $device"
else
    echo "✗ UHS-II slot not found"
    echo "Please specify device manually:"
    echo "Usage: $0 [device]"
    echo "Example: $0 /dev/sdd"
    exit 1
fi

# Allow manual device override
if [ -n "$1" ]; then
    device="$1"
    echo "Using manually specified device: $device"
fi

echo
echo "=== Device Information ==="
echo "Target device: $device"

# Verify device exists
if [ ! -b "$device" ]; then
    echo "✗ Error: $device is not a valid block device"
    exit 1
fi

echo "Requesting sudo access..."
sudo -v

# Get device information
device_size=$(sudo blockdev --getsize64 "$device")
device_sectors=$((device_size / 512))
device_mb=$((device_size / 1048576))
device_gb=$((device_size / 1073741824))

echo "Device size: ${device_mb} MB (${device_gb} GB)"
echo "Total sectors: $device_sectors"

# Calculate config location (last 2 sectors)
config_start_sector=$((device_sectors - 2))
config_end_sector=$((device_sectors - 1))

echo "SCSI2SD config location: sectors $config_start_sector-$config_end_sector"

echo
echo "=== Checking Current Configuration ==="

# Read current config to see if there's anything there
config_check_file="/tmp/scsi2sd_check.bin"
sudo dd if="$device" of="$config_check_file" bs=512 count=2 skip=$config_start_sector 2>/dev/null

if [ -f "$config_check_file" ]; then
    # Check for SCSI2SD magic signature
    magic=$(dd if="$config_check_file" bs=4 count=1 2>/dev/null | od -tx1 -An | tr -d ' ')
    
    if [ "$magic" = "42434647" ]; then
        echo "✓ Valid SCSI2SD configuration found"
        
        # Show some config details
        echo "Current configuration details:"
        
        # Read target 0 info
        target_offset=128
        scsi_id_byte=$(dd if="$config_check_file" bs=1 count=1 skip=$target_offset 2>/dev/null | od -tu1 -An | tr -d ' ')
        
        if [ -n "$scsi_id_byte" ] && [ $((scsi_id_byte & 0x80)) -ne 0 ]; then
            echo "  Target 0: Enabled"
            
            # Read identification strings
            vendor=$(dd if="$config_check_file" bs=1 count=8 skip=$((target_offset + 18)) 2>/dev/null | tr -d '\0' | sed 's/[[:space:]]*$//')
            product=$(dd if="$config_check_file" bs=1 count=16 skip=$((target_offset + 26)) 2>/dev/null | tr -d '\0' | sed 's/[[:space:]]*$//')
            revision=$(dd if="$config_check_file" bs=1 count=4 skip=$((target_offset + 42)) 2>/dev/null | tr -d '\0' | sed 's/[[:space:]]*$//')
            serial=$(dd if="$config_check_file" bs=1 count=16 skip=$((target_offset + 46)) 2>/dev/null | tr -d '\0' | sed 's/[[:space:]]*$//')
            
            echo "  Vendor: '$vendor'"
            echo "  Product: '$product'"
            echo "  Revision: '$revision'"
            echo "  Serial: '$serial'"
        else
            echo "  Target 0: Disabled"
        fi
    else
        echo "ℹ No valid SCSI2SD configuration found (magic: $magic)"
        echo "The last 2 sectors may contain other data or be empty"
    fi
    
    rm -f "$config_check_file"
else
    echo "✗ Failed to read current configuration"
    exit 1
fi

echo
echo "=== WARNING ==="
echo "This will PERMANENTLY WIPE the SCSI2SD configuration from the SD card!"
echo "The last 2 sectors (1024 bytes) will be zeroed out."
echo "Device: $device"
echo "Sectors to wipe: $config_start_sector-$config_end_sector"
echo

# Confirmation prompt
read -p "Are you sure you want to proceed? (type 'YES' to confirm): " confirmation

if [ "$confirmation" != "YES" ]; then
    echo "Operation cancelled."
    exit 0
fi

echo
echo "=== Wiping SCSI2SD Configuration ==="

# Create a file with 1024 zeros
zero_file="/tmp/scsi2sd_zeros.bin"
dd if=/dev/zero of="$zero_file" bs=1024 count=1 2>/dev/null

echo "Writing zeros to sectors $config_start_sector-$config_end_sector..."

# Write zeros to the last 2 sectors
sudo dd if="$zero_file" of="$device" bs=512 count=2 seek=$config_start_sector conv=fsync 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✓ SCSI2SD configuration successfully wiped"
    
    # Verify the wipe
    echo "Verifying wipe..."
    verify_file="/tmp/scsi2sd_verify_wipe.bin"
    sudo dd if="$device" of="$verify_file" bs=512 count=2 skip=$config_start_sector 2>/dev/null
    
    # Check if it's all zeros
    if cmp -s "$zero_file" "$verify_file"; then
        echo "✓ Verification successful - configuration area is now empty"
    else
        echo "⚠ Verification warning - some data may remain"
        echo "First few bytes of wiped area:"
        hexdump -C "$verify_file" | head -3
    fi
    
    rm -f "$verify_file"
else
    echo "✗ Failed to wipe SCSI2SD configuration"
    exit 1
fi

# Clean up
rm -f "$zero_file"

echo
echo "=== Wipe Complete ==="
echo "The SD card no longer contains SCSI2SD configuration data."
echo "If you insert this card into a SCSI2SD device, it will use default settings."
echo
echo "To restore a configuration, use the SCSI2SD utility application or"
echo "the test-scsi2sd-xml-to-binary.sh script."
